// /utils/ContentfulApi.js

export default class ContentfulApi {
  static async callContentful(query, isPreviewMode = false) {
    // const fetchUrl = `https://graphql.contentful.com/content/v1/spaces/${process.env.CONTENTFUL_SPACE_ID}`;
    const fetchUrl = `https://graphql.contentful.com/content/v1/spaces/${process.env.CONTENTFUL_SPACE_ID}/environments/${process.env.CONTENTFUL_ENVIRONMENT}`;

    const fetchOptions = {
      method: "POST",
      headers: {
        Authorization: `Bearer ${isPreviewMode ? process.env.CONTENTFUL_PREVIEW_TOKEN : process.env.CONTENTFUL_ACCESS_TOKEN}`,
        "Content-Type": "application/json",
      },
      body: JSON.stringify({ query }),
    };

    try {
      const data = await fetch(fetchUrl, fetchOptions).then((response) =>
        response.json()
      );
      return data;
    } catch (error) {
      throw new Error("Could not fetch data from Contentful!");
    }
  }

  static GetHeroImageQuery(collection, locale = "", isPreviewMode = false) {
    const query = `{
      ${collection} (locale:"${locale}", preview:${isPreviewMode}) {
        items{
          title{
            json
          }
          heroImage{
            url
            description
          }
          description
          name
        }
      }
  }`;
    return query;
  }
  // Get Community Partners
  static async GetCommunityPartners(locale, isPreviewMode = false) {
    const query = `{
        communityPartnersCollection(order:sys_publishedAt_DESC, locale:"${locale}", preview:${isPreviewMode}) {
            items {
              title
              content
              url
              image {
                url
                description
              }
            }
          }
    }`;

    const response = await this.callContentful(query, isPreviewMode);

    const communityPartners = response.data.communityPartnersCollection
      ? response.data.communityPartnersCollection
      : { total: 0, items: [] };

    return communityPartners;
  }

  // Get Partners
  static async GetPartners(locale, isPreviewMode = false) {
    const query = `{
        partnersCollection(order:sys_publishedAt_ASC, locale:"${locale}", preview:${isPreviewMode}) {
            items {
              title
              content
              url
              image {
                url
                description
              }
            }
          }
    }`;

    const response = await this.callContentful(query, isPreviewMode);

    const partners = response.data.partnersCollection
      ? response.data.partnersCollection
      : { total: 0, items: [] };

    return partners;
  }

  // Get Featured News
  static async GetFeaturedNews(locale, isPreviewMode = false) {
    const query = `{
      featuredNewsCollection(locale:"${locale}", preview:${isPreviewMode}) {
        items {
          title
          blogsCollection {
            items {
              ... on Blog {
                title
                slug
                tags
                blogImage {
                  url
                }
              }
              ... on BlogWithQuotesAndImages {
                title
                slug
                tags
                blogImage {
                  url
                }
              }
            }
          }
        }
      }
      
    }`;

    const response = await this.callContentful(query, isPreviewMode);
    const partners = response.data.featuredNewsCollection
      ? response.data.featuredNewsCollection
      : { total: 0, items: [] };

    return partners;
  }

  // Get Blogs
  static async GetBlogs(locale = "", isPreviewMode = false) {
    const query = `{
      blogCollection(order: sys_publishedAt_DESC, locale: "${locale}",preview:${isPreviewMode}) {
        items {
          sys {
            id
            publishedAt
            firstPublishedAt
          }
          title
          slug
          description {
            json
          }
          author
          tags
          secondaryTags
          blogImage {
            url
            description
          }
          bannerImageCollection {
            items {
              url
              description
            }
          }
          metaTitle
          metaRobots
          metaKeywords
          metaDescription
          metaCanonicalUrl
        }
      }
    }
    `;

    const response = await this.callContentful(query, isPreviewMode);

    const partners = response.data.blogCollection
      ? response.data.blogCollection
      : { total: 0, items: [] };

    return partners;
  }

  // Get Art Blogs
  static async GetArtBlogs(locale = "", isPreviewMode = false) {
    const query = `{
      blogCollection(order: sys_publishedAt_DESC, locale: "${locale}",preview:${isPreviewMode}, where:{secondaryTags_contains_some: ["Art","Arte"]}) {
        items {
          sys {
            id
            publishedAt
            firstPublishedAt
          }
          title
          slug
          description {
            json
          }
          author
          tags
          secondaryTags
          blogImage {
            url
            description
          }
          bannerImageCollection {
            items {
              url
              description
            }
          }
          metaTitle
          metaRobots
          metaKeywords
          metaDescription
          metaCanonicalUrl
        }
      }
    }
    `;

    const response = await this.callContentful(query, isPreviewMode);

    const partners = response.data.blogCollection
      ? response.data.blogCollection
      : { total: 0, items: [] };

    return partners;
  }

  // Get Art Background
  static async GetArtBackground(isPreviewMode = false) {
    const query = `{
        artBackgroundImageCollection {
          items {
            backgroundImage {
              title
              url
            }
          }
        }
      }
      `;

    const response = await this.callContentful(query, isPreviewMode);

    const background = response.data.artBackgroundImageCollection
      ? response.data.artBackgroundImageCollection
      : { total: 0, items: [] };

    return background;
  }

  // Get Events
  static async GetEvents(locale = "", isPreviewMode = false) {
    const query = `{
      eventsCollection(locale: "${locale}", order: date_ASC,preview:${isPreviewMode}) {
        items {
          sys {
            id
            publishedAt
            firstPublishedAt
          }
          title
          date
          eventLink
          eventImage {
            url
            description
          }
          outreachVideo
        }
      }
    }
    `;

    const response = await this.callContentful(query, isPreviewMode);
    const events = response.data.eventsCollection
      ? response.data.eventsCollection.items
      : { total: 0, items: [] };

    return events;
  }

  // Get Blogs With Quotes And Images
  static async GetBlogsWithQuotesAndImages(locale = "", isPreviewMode = false) {
    const query = `{
      blogWithQuotesAndImagesCollection(order:sys_publishedAt_DESC, locale:"${locale}",preview:${isPreviewMode}) {
        items {
          sys {
            id
            publishedAt
            firstPublishedAt
          }
          title
          image1 {
            url
            description
          }
          image2 {
            url
            description
          }
          bannerImageCollection {
            items {
              url
              description
            }
          }
          tags
          secondaryTags
          paragraph1 {
            json
          }
          paragraph2 {
            json
          }
          paragraph3 {
            json
          }
          paragraph4 {
            json
          }
          subTitle
          quote1
          quote2
          slug
          isBlogWithQuoteAndImage
          blogImage {
            url
          }
          blogVideo {
            url
          }
          metaTitle
          metaRobots
          metaKeywords
          metaDescription
          metaCanonicalUrl
        }
      }
    }`;

    const response = await this.callContentful(query, isPreviewMode);

    const partners = response.data.blogWithQuotesAndImagesCollection
      ? response.data.blogWithQuotesAndImagesCollection
      : { total: 0, items: [] };

    return partners;
  }

  // Get Art Blogs With Quotes And Images
  static async GetArtBlogsWithQuotesAndImages(locale = "", isPreviewMode = false) {
    const query = `{
      blogWithQuotesAndImagesCollection(order:sys_publishedAt_DESC, locale:"${locale}",preview:${isPreviewMode}, where:{secondaryTags_contains_some: ["Art","Arte"]}) {
        items {
          sys {
            id
            publishedAt
            firstPublishedAt
          }
          title
          image1 {
            url
            description
          }
          image2 {
            url
            description
          }
          bannerImageCollection {
            items {
              url
              description
            }
          }
          tags
          secondaryTags
          paragraph1 {
            json
          }
          paragraph2 {
            json
          }
          paragraph3 {
            json
          }
          paragraph4 {
            json
          }
          subTitle
          quote1
          quote2
          slug
          isBlogWithQuoteAndImage
          blogImage {
            url
          }
          blogVideo {
            url
          }
          metaTitle
          metaRobots
          metaKeywords
          metaDescription
          metaCanonicalUrl
        }
      }
    }`;

    const response = await this.callContentful(query, isPreviewMode);

    const partners = response.data.blogWithQuotesAndImagesCollection
      ? response.data.blogWithQuotesAndImagesCollection
      : { total: 0, items: [] };

    return partners;
  }

  // Get Blogs With Quotes And Images
  static async GetBlogsByFieldsWithQuotesAndImages(slug, locale, isPreviewMode = false) {
    const query = `{
      blogWithQuotesAndImagesCollection(where: {slug: "${slug}"}, locale:"${locale}",preview:${isPreviewMode}) {
        items {
          sys {
            id
            publishedAt
            firstPublishedAt
          }
          title
          image1 {
            url
            description
          }
          image2 {
            url
            description
          }
          bannerImageCollection {
            items {
              url
              description
            }
          }
          tags
          secondaryTags
          paragraph1 {
            json
          }
          paragraph2 {
            json
          }
          paragraph3 {
            json
          }
          paragraph4 {
            json
          }
          subTitle
          quote1
          quote2
          slug
          isBlogWithQuoteAndImage
          blogImage {
            url
          }
          blogVideo {
            url
          }
          metaTitle
          metaRobots
          metaKeywords
          metaDescription
          metaCanonicalUrl
        }
      }
    }`;

    const response = await this.callContentful(query, isPreviewMode);

    const partners = response.data.blogWithQuotesAndImagesCollection
      ? response.data.blogWithQuotesAndImagesCollection
      : { total: 0, items: [] };

    return partners;
  }

  // Get Blogs By Fields
  static async GetBlogsByFields(slug, locale, isPreviewMode = false) {
    const query = `{
      blogCollection(where: {slug: "${slug}"}, locale: "${locale}",preview:${isPreviewMode}) {
        items {
          sys {
            id
            publishedAt
            firstPublishedAt
          }
          title
          slug
          description {
            json
          }
          author
          tags
          secondaryTags
          blogImage {
            url
            description
          }
          bannerImageCollection{
            items{
              url
              description
            }
          }
          metaTitle
          metaRobots
          metaKeywords
          metaDescription
          metaCanonicalUrl
        }
      }
    }`;

    const response = await this.callContentful(query, isPreviewMode);
    const blogs = response.data.blogCollection
      ? response.data.blogCollection
      : { total: 0, items: [] };

    return blogs;
  }

  // Get Home Page Hero Images
  static async GetHeroImagesForHome(locale, isPreviewMode = false) {
    const query = `{
      homeHeroCollection(locale:"${locale}",preview:${isPreviewMode}) {
        items {
          title {
            json
          }
          description
          url
          heroImage {
            url
            description
          }
        }
      }
    }`;

    const response = await this.callContentful(query, isPreviewMode);

    const partners = response.data.homeHeroCollection
      ? response.data.homeHeroCollection
      : { total: 0, items: [] };

    return partners;
  }

  // Get About Page Hero Image
  static async GetAboutHeroImage(locale, isPreviewMode = false) {
    const query = this.GetHeroImageQuery("aboutHeroCollection", locale, isPreviewMode);

    const response = await this.callContentful(query, isPreviewMode);

    const aboutHero = response.data.aboutHeroCollection
      ? response.data.aboutHeroCollection
      : { total: 0, items: [] };

    return aboutHero;
  }

  // Get Events Page Hero Image
  static async GetEventsHeroImage(locale, isPreviewMode = false) {
    const query = this.GetHeroImageQuery("eventsHeroImageCollection", locale, isPreviewMode);

    const response = await this.callContentful(query, isPreviewMode);

    const eventHero = response.data.eventsHeroImageCollection
      ? response.data.eventsHeroImageCollection.items[0]
      : { total: 0, items: [] };

    return eventHero;
  }

  // Get About Page Slider Images
  static async GetAboutSliderImages(isPreviewMode = false) {
    const query = `{
      aboutSliderImagesCollection(preview:${isPreviewMode}){
        items{
          title
          image{
            url
            description
          }
        }
      }
    }`;

    const response = await this.callContentful(query, isPreviewMode);

    const aboutHero = response.data.aboutSliderImagesCollection
      ? response.data.aboutSliderImagesCollection
      : { total: 0, items: [] };

    return aboutHero;
  }

  // Get Business Opportunities Page Hero Image
  static async GetBusinessOpportunitiesHeroImage(locale, isPreviewMode = false) {
    const query = this.GetHeroImageQuery(
      "businessOpportunitiesHeroCollection",
      locale, isPreviewMode
    );

    const response = await this.callContentful(query, isPreviewMode);

    const businessopp = response.data.businessOpportunitiesHeroCollection
      ? response.data.businessOpportunitiesHeroCollection
      : { total: 0, items: [] };

    return businessopp;
  }

  // Get Employment Opportunities Page Hero Image
  static async GetEmploymentOpportunitiesHeroImage(locale, isPreviewMode = false) {
    const query = this.GetHeroImageQuery(
      "employmentOpportunitiesHeroCollection",
      locale,
      isPreviewMode
    );

    const response = await this.callContentful(query, isPreviewMode);

    const employmentopp = response.data.employmentOpportunitiesHeroCollection
      ? response.data.employmentOpportunitiesHeroCollection
      : { total: 0, items: [] };

    return employmentopp;
  }

  // Get Community Page Hero Image
  static async GetCommunityHeroImage(locale, isPreviewMode = false) {
    const query = this.GetHeroImageQuery("communityHeroCollection", locale, isPreviewMode);

    const response = await this.callContentful(query, isPreviewMode);

    const community = response.data.communityHeroCollection
      ? response.data.communityHeroCollection
      : { total: 0, items: [] };

    return community;
  }

  // Get Updates Page Hero Image
  static async GetUpdatesHeroImage(locale, isPreviewMode = false) {
    const query = this.GetHeroImageQuery("updatesHeroCollection", locale, isPreviewMode);

    const response = await this.callContentful(query, isPreviewMode);

    const updates = response.data.updatesHeroCollection
      ? response.data.updatesHeroCollection
      : { total: 0, items: [] };

    return updates;
  }

  // Get Updates Page Hero Image
  static async GetArtHeroImage(locale, isPreviewMode = false) {
    const query = this.GetHeroImageQuery("artHeroCollection", locale, isPreviewMode);

    const response = await this.callContentful(query, isPreviewMode);

    const arts = response.data.artHeroCollection
      ? response.data.artHeroCollection
      : { total: 0, items: [] };

    return arts;
  }

  // Get Contact Page Hero Image
  static async GetContactHeroImage(locale, isPreviewMode = false) {
    const query = this.GetHeroImageQuery("contactUsHeroCollection", locale, isPreviewMode);

    const response = await this.callContentful(query, isPreviewMode);

    const contact = response.data.contactUsHeroCollection
      ? response.data.contactUsHeroCollection
      : { total: 0, items: [] };

    return contact;
  }

  // Get My T8 Page Hero Image
  static async GetMyT8HeroImage(locale, isPreviewMode = false) {
    const query = this.GetHeroImageQuery("myT8HeroImageCollection", locale, isPreviewMode);

    const response = await this.callContentful(query, isPreviewMode);

    const contact = response.data.myT8HeroImageCollection
      ? response.data.myT8HeroImageCollection
      : { total: 0, items: [] };

    return contact;
  }
  // Get Header Top Partners
  static async GetHeaderTopPartners(isPreviewMode = false) {
    const query = `{
      headerTopPartnersCollection(preview:${isPreviewMode}) {
        items {
          title
          logosCollection(limit:10) {
            items {
              title
              url
              logo{
                title
                url
                description
              }
              logoWidth
            }
          }
        }
      }
    }`;

    const response = await this.callContentful(query, isPreviewMode);

    const topPartners = response.data.headerTopPartnersCollection
      ? response.data.headerTopPartnersCollection
      : { total: 0, items: [] };

    return topPartners;
  }

  // Get Home Page Features
  static async GetHomePageFeatures(locale, isPreviewMode = false) {
    const query = `{
      homeFeaturesCollection(locale:"${locale}",preview:${isPreviewMode}){
        items{
          title
          featuresCollection(limit:10){
            items{
              title
              link
              logo {
                url
                description
              }
              description
            }
          }
        }
      }
    }`;

    const response = await this.callContentful(query, isPreviewMode);

    const features = response.data.homeFeaturesCollection
      ? response.data.homeFeaturesCollection
      : { total: 0, items: [] };

    return features;
  }

  // Get Home Page Meta
  static async GetHomePageMeta(locale, isPreviewMode = false) {
    const query = `{
      seoMetaHomePageCollection(locale: "${locale}",preview:${isPreviewMode}) {
        items {
          title
          description
          keywords
          canonicalUrl
          robots
        }
      }
    }
    `;

    const response = await this.callContentful(query, isPreviewMode);

    const meta = response.data.seoMetaHomePageCollection
      ? response.data.seoMetaHomePageCollection.items[0]
      : { total: 0, items: [] };

    return meta;
  }

  // Get About Page Meta
  static async GetAboutPageMeta(locale, isPreviewMode = false) {
    const query = `{
      seoMetaAboutCollection(locale: "${locale}",preview:${isPreviewMode}) {
        items {
          title
          description
          keywords
          canonicalUrl
          robots
        }
      }
    }
    `;

    const response = await this.callContentful(query, isPreviewMode);

    const meta = response.data.seoMetaAboutCollection
      ? response.data.seoMetaAboutCollection.items[0]
      : { total: 0, items: [] };

    return meta;
  }

  // Get Event Page Meta
  static async GetEventPageMeta(locale, isPreviewMode = false) {
    const query = `{
      seoMetaEventsPageCollection(locale: "${locale}",preview:${isPreviewMode}) {
        items {
          title
          description
          keywords
          canonicalUrl
          robots
        }
      }
    }
    `;

    const response = await this.callContentful(query, isPreviewMode);

    const meta = response.data.seoMetaEventsPageCollection
      ? response.data.seoMetaEventsPageCollection.items[0]
      : { total: 0, items: [] };

    return meta;
  }
  // Get Business Opportunities Page Meta
  static async GetBusinessOpportunitiesPageMeta(locale, isPreviewMode = false) {
    const query = `{
      seoMetaBusinessOpportunitiesCollection(locale: "${locale}", preview:${isPreviewMode}) {
        items {
          title
          description
          keywords
          canonicalUrl
          robots
        }
      }
    }
    `;

    const response = await this.callContentful(query, isPreviewMode);

    const meta = response.data.seoMetaBusinessOpportunitiesCollection
      ? response.data.seoMetaBusinessOpportunitiesCollection.items[0]
      : { total: 0, items: [] };

    return meta;
  }

  // Get Employment Opportunities Page Meta
  static async GetEmploymentOpportunitiesPageMeta(locale, isPreviewMode = false) {
    const query = `{
      seoMetaEmploymentOpportunitiesCollection(locale: "${locale}",preview:${isPreviewMode}) {
        items {
          title
          description
          keywords
          canonicalUrl
          robots
        }
      }
    }
    `;

    const response = await this.callContentful(query, isPreviewMode);

    const meta = response.data.seoMetaEmploymentOpportunitiesCollection
      ? response.data.seoMetaEmploymentOpportunitiesCollection.items[0]
      : { total: 0, items: [] };

    return meta;
  }

  // Get Community Page Meta
  static async GetCommunityPageMeta(locale, isPreviewMode = false) {
    const query = `{
        seoMetaCommunityCollection(locale: "${locale}",preview:${isPreviewMode}) {
          items {
            title
            description
            keywords
            canonicalUrl
            robots
          }
        }
      }
      `;

    const response = await this.callContentful(query, isPreviewMode);

    const meta = response.data.seoMetaCommunityCollection
      ? response.data.seoMetaCommunityCollection.items[0]
      : { total: 0, items: [] };

    return meta;
  }

  // Get News Page Meta
  static async GetNewsPageMeta(locale, isPreviewMode = false) {
    const query = `{
        seoMetaNewsCollection(locale: "${locale}",preview:${isPreviewMode}) {
          items {
            title
            description
            keywords
            canonicalUrl
            robots
          }
        }
      }
      `;

    const response = await this.callContentful(query, isPreviewMode);

    const meta = response.data.seoMetaNewsCollection
      ? response.data.seoMetaNewsCollection.items[0]
      : { total: 0, items: [] };

    return meta;
  }


  // Get Contact Page Meta
  static async GetContactPageMeta(locale, isPreviewMode = false) {
    const query = `{
      seoMetaContactUsCollection(locale: "${locale}",preview:${isPreviewMode}) {
          items {
            title
            description
            keywords
            canonicalUrl
            robots
          }
        }
      }
      `;

    const response = await this.callContentful(query, isPreviewMode);

    const meta = response.data.seoMetaContactUsCollection
      ? response.data.seoMetaContactUsCollection.items[0]
      : { total: 0, items: [] };

    return meta;
  }

  // Get My T8 Page Meta
  static async GetMyT8PageMeta(locale, isPreviewMode = false) {
    const query = `{
      seoMetaMyT8Collection(locale: "${locale}",preview:${isPreviewMode}) {
          items {
            title
            description
            keywords
            canonicalUrl
            robots
          }
        }
      }
      `;

    const response = await this.callContentful(query, isPreviewMode);

    const meta = response.data.seoMetaMyT8Collection
      ? response.data.seoMetaMyT8Collection.items[0]
      : { total: 0, items: [] };

    return meta;
  }

  // Get My T8 Page Meta
  static async GetMyT8ConcessionPageMeta(locale, isPreviewMode = false) {
    const query = `{
    seoMetaMyT8ConcessionCollection(locale: "${locale}",preview:${isPreviewMode}) {
        items {
          title
          description
          keywords
          canonicalUrl
          robots
        }
      }
    }
    `;

    const response = await this.callContentful(query, isPreviewMode);

    const meta = response.data.seoMetaMyT8ConcessionCollection
      ? response.data.seoMetaMyT8ConcessionCollection.items[0]
      : { total: 0, items: [] };

    return meta;
  }
  // Get About Page Features
  static async GetAboutPageFeatures(locale, isPreviewMode = false) {
    const query = `{
      aboutFeaturesCollection(locale:"${locale}", preview:${isPreviewMode}) {
        items {
          title
          featuresCollection(limit: 10) {
            items {
              title
              link
              logo {
                url
                description
              }
              description
            }
          }
        }
      }
    }`;

    const response = await this.callContentful(query, isPreviewMode);

    const aboutFeatures = response.data.aboutFeaturesCollection
      ? response.data.aboutFeaturesCollection
      : { total: 0, items: [] };

    return aboutFeatures;
  }

  // Get Community Page Features
  static async GetCommunityPageFeatures(locale, isPreviewMode = false) {
    const query = `{
      communityFeaturesCollection(locale:"${locale}",preview:${isPreviewMode}) {
        items {
          title
          featuresCollection(limit: 10) {
            items {
              title
              link
              logo {
                url
                description
              }
              description
            }
          }
        }
      }
    }`;

    const response = await this.callContentful(query, isPreviewMode);

    const communityFeatures = response.data.communityFeaturesCollection
      ? response.data.communityFeaturesCollection
      : { total: 0, items: [] };

    return communityFeatures;
  }

  // Get My T8 Page Features
  static async GetMyT8Features(locale, isPreviewMode = false) {
    const query = `{
        myT8FeaturesCollection(locale:"${locale}", preview:${isPreviewMode}) {
          items {
            title
            featuresCollection(limit: 10) {
              items {
                title
                link
                logo {
                  url
                  description
                }
                description
              }
            }
          }
        }
    }`;

    const response = await this.callContentful(query, isPreviewMode);

    const communityFeatures = response.data.myT8FeaturesCollection
      ? response.data.myT8FeaturesCollection
      : { total: 0, items: [] };

    return communityFeatures;
  }
  // Get Opportunities Banner Section
  static async GetOpportunitiesBannerSection(locale, isPreviewMode = false) {
    const query = `{
      opportunitiesCollection(locale:"${locale}",preview:${isPreviewMode}) {
        items{
          title
          url
          image{
            url
            description
          }
          subContent{
            json
          }
        }
      }
    }`;

    const response = await this.callContentful(query, isPreviewMode);

    const opportunities = response.data.opportunitiesCollection
      ? response.data.opportunitiesCollection
      : { total: 0, items: [] };

    return opportunities;
  }

  // Get Footer Partner Logos
  static async GetFooterPartnerLogos(isPreviewMode = false) {
    const query = `{
      footerPartnerLogosCollection(preview:${isPreviewMode}){
        items{
          title
          partnersCollection(limit:10){
            items{
              title
              logo{
                url
                description
              }
              url
              logoWidth
            }
          }
        }
      }
    }`;

    const response = await this.callContentful(query, isPreviewMode);

    const footerPartnerLogos = response.data.footerPartnerLogosCollection
      ? response.data.footerPartnerLogosCollection
      : { total: 0, items: [] };

    return footerPartnerLogos;
  }

  // Get Footer Community Partner Logos
  static async GetFooterCommunityPartnerLogos(isPreviewMode = false) {
    const query = `{
      footerCommunityPartnerLogosCollection(preview:${isPreviewMode}) {
        items {
          title
          communityPartnersCollection(limit: 10) {
            items {
              title
              logo {
                url
                description
              }
              url
              logoWidth
            }
          }
        }
      }
    }`;

    const response = await this.callContentful(query, isPreviewMode);

    const footerCommunityPartnerLogos = response.data
      .footerCommunityPartnerLogosCollection
      ? response.data.footerCommunityPartnerLogosCollection
      : { total: 0, items: [] };

    return footerCommunityPartnerLogos;
  }

  // Get Business Opportunities Two Text Section
  static async GetBusinessOpportunitiesTextSection(locale, isPreviewMode = false) {
    const query = `{
      businessOpportunitiesTwoTextSectionCollection(locale:"${locale}", preview:${isPreviewMode}) {
        items {
          title
          description
        }
      }
    }`;

    const response = await this.callContentful(query, isPreviewMode);

    const textSection = response.data
      .businessOpportunitiesTwoTextSectionCollection
      ? response.data.businessOpportunitiesTwoTextSectionCollection
      : { total: 0, items: [] };

    return textSection;
  }

  // Get Employment Opportunities Two Text Section
  static async GetEmploymentOpportunitiesTextSection(locale, isPreviewMode = false) {
    const query = `{
      employmentOpportunitiesTwoTextSectionCollection(locale:"${locale}",preview:${isPreviewMode}) {
        items {
          title
          description
        }
      }
    }`;

    const response = await this.callContentful(query, isPreviewMode);

    const textSection = response.data
      .employmentOpportunitiesTwoTextSectionCollection
      ? response.data.employmentOpportunitiesTwoTextSectionCollection
      : { total: 0, items: [] };

    return textSection;
  }

  // Get MyT8 Stories
  static async GetMyT8Stories(locale, isPreviewMode = false) {
    const query = `{
      myT8StoriesCollection(order: sys_publishedAt_DESC, locale:"${locale}",preview:${isPreviewMode}) {
        items {
          title
          content
          url
          image {
            url
            description
          }
        }
      }
    }`;

    const response = await this.callContentful(query, isPreviewMode);

    const communityPartners = response.data.myT8StoriesCollection
      ? response.data.myT8StoriesCollection
      : { total: 0, items: [] };

    return communityPartners;
  }

  // Get Slugs for Pagination
  static async GetSlugsForPagination(locale, isPreviewMode = false) {
    const query = `{
      blogCollection(order: sys_publishedAt_DESC,locale:"${locale}",preview:${isPreviewMode}) {
        items {
          sys {
            publishedAt
          }
          slug
        }
      }
      blogWithQuotesAndImagesCollection(order: sys_publishedAt_DESC,locale:"${locale}",preview:${isPreviewMode}) {
        items {
          sys {
            publishedAt
          }
          slug
        }
      }
    }`;

    const response = await this.callContentful(query, isPreviewMode);

    const blogs = response.data.blogCollection
      ? response.data.blogCollection
      : { total: 0, items: [] };

    const blogsWithQuoteAndImage = response.data
      .blogWithQuotesAndImagesCollection
      ? response.data.blogWithQuotesAndImagesCollection
      : { total: 0, items: [] };

    let allBlogs = [...blogs?.items, ...blogsWithQuoteAndImage?.items];
    allBlogs = allBlogs.map((item) => {
      return { slug: item.slug, publishedAt: item.sys.publishedAt };
    })
      .sort(function (a, b) {
        return new Date(b.publishedAt) - new Date(a.publishedAt);
      });

    return allBlogs;
  }

  // Get FAQs
  static async GetFAQ(locale, isPreviewMode = false) {
    const query = `{
      faqCollection(locale: "${locale}", preview: ${isPreviewMode}) {
        items {
          question
          answer{
            json
          }
        }
      }
    }`;

    const response = await this.callContentful(query, isPreviewMode);

    const faqCollection = response.data.faqCollection
      ? response.data.faqCollection
      : { total: 0, items: [] };

    return faqCollection;
  }
}
