const YOUR_URL = "https://www.thenewjfkt8.com";
const getDate = new Date().toISOString();
const fs = require("fs");
const globby = require("globby");
const i18nextConfig = require("../next-i18next.config");

async function generateSiteMap() {
  const blogs = await GetBlogs();
  const blogsWithQuoteAndImage = await GetBlogsWithQuotesAndImages();
  const allBlogs = [...blogs?.items, ...blogsWithQuoteAndImage?.items];

  const paths = [];
  i18nextConfig.i18n.locales.map((locale) => {
    return allBlogs.map((item) => {
      return paths.push({ slug: item.slug, locale: locale });
    });
  });

  const blogList = `
          ${paths
            .map((item) => {
              return `
                <url>
                  <loc>${`${YOUR_URL}/${item.locale}/blog/${item.slug}`}</loc>
                  <lastmod>${getDate}</lastmod>
                </url>`;
            })
            .join("")}
        `;

  // Send a list of paths to globby for it to read
  // We add an ! before the files we want to be ignored
  // change the file path to match the files in your own project
  const pages = await globby([
    "src/pages/**/*.jsx",
    "!src/pages/_*.jsx",
    "!src/pages/api",
  ]);

  const sitemap = `<?xml version="1.0" encoding="UTF-8"?>
      <urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9" xmlns:xhtml="http://www.w3.org/1999/xhtml">
          ${i18nextConfig.i18n.locales.map((locale) => {
            return pages
              .map((page) => {
                const regex = /(.pages)|(src)|(.jsx)|(.md)|(.index)/gi;
                let route = page.replace(regex, "");
                route = route.toLowerCase() == "/updates" ? "/news" : route; 
                return `
                        <url>
                            <loc>${`${YOUR_URL}/${locale}${route.toLowerCase()}`}</loc>
                            <lastmod>${getDate}</lastmod>
                        </url>
                    `;
              }).join("");
          }).join("")}
          ${blogList}
      </urlset>
  `;
  
  fs.writeFileSync("public/assets/sitemap.xml", sitemap);
}

const callContentful = async (query) => {
  // const fetchUrl = `https://graphql.contentful.com/content/v1/spaces/${process.env.CONTENTFUL_SPACE_ID}`;
  const fetchUrl = `https://graphql.contentful.com/content/v1/spaces/${process.env.CONTENTFUL_SPACE_ID}/environments/${process.env.CONTENTFUL_ENVIRONMENT}`;

  const fetchOptions = {
    method: "POST",
    headers: {
      Authorization: `Bearer ${process.env.CONTENTFUL_ACCESS_TOKEN}`,
      "Content-Type": "application/json",
    },
    body: JSON.stringify({ query }),
  };

  try {
    const data = await fetch(fetchUrl, fetchOptions).then((response) =>
      response.json()
    );
    return data;
  } catch (error) {
    throw new Error("Could not fetch data from Contentful!");
  }
};

// Get Blogs With Quotes And Images
const GetBlogsWithQuotesAndImages = async () => {
  const blogWithQuotesAndImagesQuery = `{
    blogWithQuotesAndImagesCollection(order: sys_publishedAt_DESC) {
      items {
        slug
      }
    }
  }`;

  const response = await callContentful(blogWithQuotesAndImagesQuery);

  const blogs = response.data.blogWithQuotesAndImagesCollection
    ? response.data.blogWithQuotesAndImagesCollection
    : { total: 0, items: [] };

  return blogs;
};

// Get Blogs
const GetBlogs = async () => {
  const blogQuery = `{
    blogCollection(order: sys_publishedAt_DESC) {
      items {
        slug
      }
    }
  }`;

  const response = await callContentful(blogQuery);

  const blogs = response.data.blogCollection
    ? response.data.blogCollection
    : { total: 0, items: [] };

  return blogs;
};

generateSiteMap();
