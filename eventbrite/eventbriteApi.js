export default class EventBriteApi {
  static async getAllEvents() {
    // const fetchUrl = `https://graphql.contentful.com/content/v1/spaces/${process.env.CONTENTFUL_SPACE_ID}`;
    const fetchUrl = `https://www.eventbriteapi.com/v3/organizations/${process.env.EVENTBRITE_ORGANIZATION_ID}/events/`;

    const fetchOptions = {
      method: "GET",
      headers: {
        Authorization: `Bearer ${process.env.EVENTBRITE_ACCESS_TOKEN}`,
        "Content-Type": "application/json",
      },
    };

    try {
      let data = await fetch(fetchUrl, fetchOptions).then((response) => response.json());
    //   console.log(data)
      // console.log({...data, events : data.events.filter(x => new Date(x.start.local) >= new Date())})
      data = {...data, events : data.events.filter(x => new Date(x.start.local) >= new Date())}
      return data;
    } catch (error) {
      throw new Error("Could not fetch data from EventBrite!");
    }
  }
}
