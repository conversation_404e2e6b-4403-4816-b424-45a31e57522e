#------------------------------------------------------------------------------------
# SNS
#------------------------------------------------------------------------------------
resource "aws_sns_topic" "main" {
  name = var.name
}

resource "aws_sns_topic_subscription" "main" {
  topic_arn = aws_sns_topic.main.arn
  protocol  = "lambda"
  endpoint  = var.lambda_arn
}

resource "aws_lambda_permission" "permission" {
  statement_id  = "AllowInvocation"
  action        = "lambda:InvokeFunction"
  function_name = var.lambda_arn
  source_arn    = aws_sns_topic.main.arn
  principal     = "sns.amazonaws.com"
}

#------------------------------------------------------------------------------------
# Outputs
#------------------------------------------------------------------------------------
output "arn" {
  value = aws_sns_topic.main.arn
}

#------------------------------------------------------------------------------------
# Variables
#------------------------------------------------------------------------------------
variable "name" {
  description = "SNS name"
  type        = string
}

variable "lambda_arn" {
  description = "Lambda ARN"
  type        = string
}