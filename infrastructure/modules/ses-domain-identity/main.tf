#------------------------------------------------------------------------------------
# Data resources to fetch values
#------------------------------------------------------------------------------------
data "aws_region" "current" {}

#------------------------------------------------------------------------------------
# Domain Identity
#------------------------------------------------------------------------------------
resource "aws_ses_domain_identity" "domain" {
  domain = var.domain
  lifecycle {
    prevent_destroy = true
  }
}

#------------------------------------------------------------------------------------
# Domain DKIM
#------------------------------------------------------------------------------------
resource "aws_ses_domain_dkim" "domain" {
  domain = aws_ses_domain_identity.domain.domain
  lifecycle {
    prevent_destroy = true
  }
}

#------------------------------------------------------------------------------------
# Mail from Domain
#------------------------------------------------------------------------------------
resource "aws_ses_domain_mail_from" "bounce" {
  domain           = aws_ses_domain_identity.domain.domain
  mail_from_domain = "bounce.${aws_ses_domain_identity.domain.domain}"
}

#------------------------------------------------------------------------------------
# SES Notification
#------------------------------------------------------------------------------------
## Delivery
resource "aws_ses_identity_notification_topic" "notification" {
  for_each = var.notification

  topic_arn                = each.value["topic_arn"]
  notification_type        = each.key
  identity                 = aws_ses_domain_identity.domain.domain
  include_original_headers = true
}

#------------------------------------------------------------------------------------
# Outputs
#------------------------------------------------------------------------------------
output "domain" {
  value = aws_ses_domain_identity.domain.domain
}

output "arn" {
  value = aws_ses_domain_identity.domain.arn
}

#------------------------------------------------------------------------------------
# Variables
#------------------------------------------------------------------------------------
variable "domain" {
  description = "Domain name"
  type        = string
}

variable "notification" {
  description = "Notification Config"
  type        = map(any)
}
