## Include terragrunt config files
include "root" {
  path = find_in_parent_folders()
}

## Local values
locals {
  envvars              = read_terragrunt_config(find_in_parent_folders("env.hcl"))
  globalvars           = read_terragrunt_config(find_in_parent_folders("global.hcl"))
  resource_prefix      = local.envvars.locals.resource_prefix
  env                  = local.envvars.locals.env
  project              = local.globalvars.locals.project
  aws_provider_version = local.globalvars.locals.aws_provider_version
}

## Terraform config
terraform {
  source = "../../../..//modules/lambda"
}

## Dependencies
dependency "s3_lambda_artifacts" {
  config_path                             = "../s3-lambda-artifacts"
  mock_outputs_allowed_terraform_commands = get_terraform_commands_that_need_input()
  mock_outputs = {
    id = "id"
  }
  mock_outputs_merge_strategy_with_state = "shallow"
}

## Provider versions config
generate "versions" {
  path      = "versions.tf"
  if_exists = "overwrite"
  contents  = <<EOF
terraform {
  required_providers {
    aws = {
      source  = "hashicorp/aws"
      version = "~> ${local.aws_provider_version}"
    }
  }
}
EOF
}

inputs = {
  function_name           = "${local.resource_prefix}-website"
  description             = "Website"
  handler                 = "index.handler"
  lambda_runtime          = "nodejs22.x"
  source_code             = get_env("TF_lambda_website_and_contentful_preview_source_code_file")
  lambda_artifacts_bucket = dependency.s3_lambda_artifacts.outputs.id
  lambda_timeout          = 60
  function_url            = true
  authorization_type      = "AWS_IAM"
}
