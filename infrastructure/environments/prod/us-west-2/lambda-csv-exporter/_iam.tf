#------------------------------------------------------------------------------------
# Data resources to fetch values
#------------------------------------------------------------------------------------
data "aws_region" "current" {}

data "aws_caller_identity" "current" {}

locals {
  account_id = data.aws_caller_identity.current.account_id
  aws_region = data.aws_region.current.name
}

## Dynamodb Tables
data "aws_dynamodb_table" "opportunity_response" {
  name = var.opportunity_response_dyanmodb_table
}

data "aws_dynamodb_table" "contact_response" {
  name = var.contact_response_dyanmodb_table
}

#------------------------------------------------------------------------------------
# IAM
#------------------------------------------------------------------------------------
resource "aws_iam_role_policy" "lambda_csv_exporter_additional_permissions" {
  name   = "${aws_iam_role.lambda.id}-additional-permissions"
  role   = aws_iam_role.lambda.id
  policy = data.aws_iam_policy_document.lambda_csv_exporter_additional_permissions.json
}

data "aws_iam_policy_document" "lambda_csv_exporter_additional_permissions" {
  statement {

    effect = "Allow"

    actions = [
      "s3:ListBucket"
    ]

    resources = [
      var.csv_exporter_bucket_arn
    ]
  }
  statement {

    effect = "Allow"

    actions = [
      "s3:PutObject",
      "s3:GetObject"
    ]

    resources = [
      "${var.csv_exporter_bucket_arn}/*"
    ]
  }
  statement {

    effect = "Allow"

    actions = [
      "dynamodb:Query",
    ]

    resources = concat(
      [data.aws_dynamodb_table.contact_response.arn],
      [for index, global_index in data.aws_dynamodb_table.contact_response.global_secondary_index : "${data.aws_dynamodb_table.contact_response.arn}/index/${global_index.name}"]
    )
  }
  statement {

    effect = "Allow"

    actions = [
      "dynamodb:Query",
    ]

    resources = concat(
      [data.aws_dynamodb_table.opportunity_response.arn],
      [for index, global_index in data.aws_dynamodb_table.opportunity_response.global_secondary_index : "${data.aws_dynamodb_table.opportunity_response.arn}/index/${global_index.name}"]
    )
  }
  statement {

    effect = "Allow"

    actions = [
      "ses:SendEmail"
    ]

    resources = [
      "arn:aws:ses:${local.aws_region}:${local.account_id}:identity/${var.ses_domain_identity}"
    ]

    condition {
      test     = "StringEquals"
      variable = "ses:FromAddress"
      values = [
        var.source_email
      ]
    }
  }
}