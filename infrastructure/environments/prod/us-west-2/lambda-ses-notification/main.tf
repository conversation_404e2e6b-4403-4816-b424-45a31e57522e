resource "aws_iam_role" "lambda" {
  name               = "${local.name}-lambda"
  assume_role_policy = data.aws_iam_policy_document.lambda_service_trust_policy.json
}

data "aws_iam_policy_document" "lambda_service_trust_policy" {
  statement {
    effect = "Allow"

    principals {
      type        = "Service"
      identifiers = ["lambda.amazonaws.com"]
    }

    actions = ["sts:AssumeRole"]
  }
}

resource "aws_iam_role_policy_attachment" "lambda_basic_execution" {
  role       = aws_iam_role.lambda.id
  policy_arn = "arn:aws:iam::aws:policy/service-role/AWSLambdaBasicExecutionRole"
}

resource "aws_lambda_function" "lambda" {
  function_name = local.name
  description   = "SES notification"
  handler       = "ses-logging-and-notification.lambda_handler"
  role          = aws_iam_role.lambda.arn
  runtime       = "python3.8"
  timeout       = 5
  filename      = "ses-logging-and-notification.zip"
}

locals {
  name = "${var.prefix}-ses-notification"
}

variable "prefix" {
  description = "Name prefix"
  type        = string
}

output "arn" {
  value = aws_lambda_function.lambda.arn
}