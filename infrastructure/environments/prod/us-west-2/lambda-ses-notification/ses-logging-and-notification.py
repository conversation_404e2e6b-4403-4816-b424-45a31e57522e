import json
import os
import datetime
from urllib.request import Request, urlopen

# SLACK_WEBHOOK = os.environ['SLACK_WEBHOOK']
# SLACK_CHANNEL = os.environ['SLACK_CHANNEL']

def slack(event, message):

    # Formatting time
    time = datetime.datetime.strptime(message["mail"]['timestamp'], '%Y-%m-%dT%H:%M:%S.%f%z').strftime('%Y-%m-%d %H:%M:%S %Z')
    mail =  message["mail"]
    destinationEmail = mail["destination"][0]
    sourceEmail = mail["source"]
    callerIdentity = mail["callerIdentity"]

    # message text based on event
    # Bounce
    if event == "Bounce":
        bounce = message["bounce"]

        # Checking key existance
        diagnosticCode = bounce["bouncedRecipients"][0].get("diagnosticCode", None)

        text = "{time} \n*{event}* {destinationEmail} \nDiagnostic Code: `{diagnosticCode}` \nType: `{Type}` \nSub Type: `{SubType}` \nSource: `{sourceEmail}` \nCaller Identity: `{callerIdentity}`".format(
            time = time,
            event = event,
            destinationEmail = destinationEmail,
            diagnosticCode = diagnosticCode,
            Type = bounce["bounceType"],
            SubType = bounce["bounceSubType"],
            sourceEmail = sourceEmail,
            callerIdentity = callerIdentity
            )

    # Complaint
    elif event == "Complaint":
        complaint = message["complaint"]

        # Checking key existance
        complaintFeedbackType = complaint.get("complaintFeedbackType", None)
        userAgent = complaint.get("userAgent", None)

        text = "{time} \n*{event}* {destinationEmail} \nFeedback Type: `{complaintFeedbackType}` \nUser Agent: `{userAgent}` \nSub Type: `{SubType}` \nSource: `{sourceEmail}` \nCaller Identity: `{callerIdentity}`".format(
            time = time,
            event = event,
            destinationEmail = destinationEmail,
            complaintFeedbackType = complaintFeedbackType,
            userAgent = userAgent,
            SubType = complaint["complaintSubType"],
            sourceEmail = sourceEmail,
            callerIdentity = callerIdentity
            )

    # Preparing slack message
    slack_message = {
        'channel': SLACK_CHANNEL,
        'attachments': [
            {
                'text': text,
                'color': '#ff2424'
            }
        ]
    }

    # Preparing Slack request
    request = Request(
        SLACK_WEBHOOK,
        json.dumps(slack_message).encode('utf-8')
        )

    # Calling Slack webhook
    try:
        response = urlopen(request)
        print("Response from Slack webhook: {}".format(response.read()))
    except Exception as e:
        print(e)


def lambda_handler(event, context):

    for record in event['Records']:

        # Message
        message = json.loads(record["Sns"]["Message"])
        notificationType = message["notificationType"]

        # Take action based on notificaiton type
        # Delivery
        if notificationType == "Delivery":
            print(message)

        # Bounce
        elif notificationType == "Bounce":
            print(message)
            # slack(notificationType, message)

        # Complaint
        elif notificationType == "Complaint":
            print(message)
            # slack(notificationType, message)
