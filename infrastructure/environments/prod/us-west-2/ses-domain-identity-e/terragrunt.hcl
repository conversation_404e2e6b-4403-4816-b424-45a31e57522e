## Include terragrunt config files
include "root" {
  path = find_in_parent_folders()
}

## Local values
locals {
  envvars              = read_terragrunt_config(find_in_parent_folders("env.hcl"))
  globalvars           = read_terragrunt_config(find_in_parent_folders("global.hcl"))
  prefix               = local.envvars.locals.prefix
  env                  = local.envvars.locals.env
  project              = local.globalvars.locals.project
  aws_provider_version = local.globalvars.locals.aws_provider_version
}

## Terraform config
terraform {
  source = "../../../..//modules/ses-domain-identity"
}


## Dependencies
dependency "sns_ses_notification" {
  config_path                             = "../sns-ses-notification"
  mock_outputs_allowed_terraform_commands = get_terraform_commands_that_need_input()
  mock_outputs = {
    arn = "arn"
  }
  mock_outputs_merge_strategy_with_state = "shallow"
}

## Provider versions config
generate "versions" {
  path      = "versions.tf"
  if_exists = "overwrite"
  contents  = <<EOF
terraform {
  required_providers {
    aws = {
      source  = "hashicorp/aws"
      version = "~> ${local.aws_provider_version}"
    }
  }
}
EOF
}

inputs = {
  domain = "e.urwairports.com"
  notification = {
    "Delivery" = {
      topic_arn = dependency.sns_ses_notification.outputs.arn
    },
    "Bounce" = {
      topic_arn = dependency.sns_ses_notification.outputs.arn
    },
    "Complaint" = {
      topic_arn = dependency.sns_ses_notification.outputs.arn
    }
  }
}