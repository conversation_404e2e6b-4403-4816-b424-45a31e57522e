#--------------------------------------------------------------------------------
# Cloudfront Function for Redirection traffic | root --> www
#--------------------------------------------------------------------------------
resource "aws_cloudfront_function" "root_to_www_redirection" {
  name    = var.name
  runtime = "cloudfront-js-1.0"
  comment = var.comment
  publish = true
  code    = <<EOF
function handler(event) {
  var response = {
    statusCode: 301,
    statusDescription: 'Moved Permanently',
    headers: { "location": { "value": `https://${var.redirect_domain}` } }
  }
  return response;
}
EOF
}

#--------------------------------------------------------------------------------
# Variable
#--------------------------------------------------------------------------------
variable "redirect_domain" {
  description = "Redirection domain"
  type        = string
}

variable "name" {
  description = "Function name"
  type        = string
}

variable "comment" {
  description = "Function comment"
  type        = string
}

#--------------------------------------------------------------------------------
# Outputs
#--------------------------------------------------------------------------------
output "arn" {
  value = aws_cloudfront_function.root_to_www_redirection.arn
}