## Include terragrunt config files
include "root" {
  path = find_in_parent_folders()
}

## Local values
locals {
  envvars              = read_terragrunt_config(find_in_parent_folders("env.hcl"))
  globalvars           = read_terragrunt_config(find_in_parent_folders("global.hcl"))
  envvars_             = read_terragrunt_config(find_in_parent_folders("env.hcl"))
  prefix               = local.envvars.locals.prefix
  env                  = local.envvars.locals.env
  project              = local.globalvars.locals.project
  aws_provider_version = local.globalvars.locals.aws_provider_version
  aws_profile_         = local.envvars_.locals.aws_profile
  repo_name            = local.globalvars.locals.repo_name
}

## Terraform config
terraform {
  source = "../../../..//modules/cloudfront"
}

## Provider versions config
generate "versions" {
  path      = "versions.tf"
  if_exists = "overwrite"
  contents  = <<EOF
terraform {
  required_providers {
    aws = {
      source  = "hashicorp/aws"
      version = "~> 5.50.0"
    }
  }
}
EOF
}

## AWS Provider Config
generate "provider_alias" {
  path      = "provider_alias.tf"
  if_exists = "overwrite"
  contents  = <<EOF
provider "aws" {
  profile = "${local.aws_profile_}"
  region = "us-west-2"
  alias  = "us-west-2"
  default_tags {
    tags = {
      Environment = "${local.env}"
      Owner       = "Terraform"
      RepoName    = "${local.repo_name}"
    }
  }
}
EOF
}

## Dependencies
dependency "lambda_website" {
  config_path                             = "../../us-west-2/lambda-website"
  mock_outputs_allowed_terraform_commands = get_terraform_commands_that_need_input()
  mock_outputs = {
    function_name     = "function_name",
    function_endpoint = "function_endpoint"
  }
  mock_outputs_merge_strategy_with_state = "shallow"
}

dependency "acm_airports_jfk_t8" {
  config_path                             = "../acm-airports-jfk-t8"
  mock_outputs_allowed_terraform_commands = get_terraform_commands_that_need_input()
  mock_outputs = {
    arn = "arn"
  }
  mock_outputs_merge_strategy_with_state = "shallow"
}

## Inputs
inputs = {
  origin = {
    domain_name = dependency.lambda_website.outputs.function_endpoint
    origin_id   = dependency.lambda_website.outputs.function_name

    ## for http endpoint
    custom_origin_config = {
      origin_protocol_policy = "https-only"
    }
  }

  domain_aliases = ["demo.thenewjfkt8.com"]
  acm_arn        = dependency.acm_airports_jfk_t8.outputs.arn

  ttl_values = {
    min_ttl     = 0
    max_ttl     = 31536000 # Year
    default_ttl = 604800   # Week
  }

  response_headers_policy_id = "f7a7060e-4641-421c-982e-fe57f5b59d85" # Policy created by URW IT Security

  custom_error_response = [
    {
      error_caching_min_ttl = 600
      error_code            = 404
    }
  ]

  ## for OAC permission
  prefix        = local.prefix
  function_name = dependency.lambda_website.outputs.function_name

  create_lambda_permission = true
  create_oac               = true
}
