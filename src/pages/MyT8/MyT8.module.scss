.opportunities {
    margin: 0;
    display: flex;
    flex-wrap: wrap;
  
    .col {
      padding: 0;
      float: left;
      position: relative;
      overflow: hidden;
      img {
        object-fit: cover;
        height: 100%;
        width: 80%;
      }
  
      &:after {
        content: '';
        display: block;
        width: 100%;
        height: 100%;
        position: absolute;
        top: 0;
        left: 0;
        background: rgb(248, 248, 248);
        background: linear-gradient(
          90deg,
          rgba(248, 248, 248, 0) 0%,
          rgba(248, 248, 248, 1) 70%
        );
      }
    }
  }

  .link-text {
    position: absolute;
    right: 20px;
    top: 0;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    max-width: 220px;
    z-index: 1;
  
    h2 {
      font-weight: 400;
    }
  
    margin: 0;
  
    p {
      margin: 0 0 0 40px;
    }
  }
  @media (max-width: 991px) {
    .opportunities {
      .col h2 {
        font-size: 20px;
      }
  
      .link-text {
        max-width: 170px;
      }
  
      .col .link-text p {
        margin: 0 0 0 20px;
        width: 20px;
        height: 15px;
      }
    }
  }

  @media screen and (max-width: 767px) {
    .opportunities {
        margin: 0 -20px;
        background-color: #f8f8f8;
    
        .link-text {
          h2 {
            font-size: 18px;
          }
    
          p {
            width: 20px;
            height: 12px;
            margin: 0 0 0 20px;
          }
        }
    
        .col {
          width: 100%;
        }
      }
  }

  .btn-myt8{
    line-height: 40px;

    &:hover{
        color: white;
    }
}
