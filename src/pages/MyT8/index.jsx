import Banner from "../../components/Banner";
import Features from "../../components/Features";
import React from "react";
import Card from "../../components/Card";
import FeaturedNews from "../../components/FeaturedNews";
import LinkWithImageComponent from "../../../internationalization/LinkWithImage";
import styles from "./MyT8.module.scss";
import Accordion from "../../components/common/Accordian";
const MyT8 = ({
  myT8HeroImage,
  myT8Features,
  myT8Stories,
  featuredNews,
  v,
  t,
  faqs,
}) => {
  const featuresContent =
    myT8Features?.items[0]?.featuresCollection?.items?.map((item) => {
      return (
        item && {
          title: item.title,
          link: item.link,
          imageUrl: item?.logo?.url,
          description: item?.logo?.description,
          content: item.description,
        }
      );
    });

  return (
    <>
      <Banner sectionClass="inner-banner" details={myT8HeroImage} />
      <Features content={featuresContent} />

      <LinkWithImageComponent
        href="/myt8-concession"
        linktext={t("cancel")}
        style={{ left: "180px" }}
        className={`${styles["btn-myt8"]} btn btn-secondary`}
      >
        <section className="contact-info contact-form">
          <div className="container">
            <React.Fragment>
              <div className={`row ${styles["opportunities"]}`}>
                <div className={`${styles["col"]} col-6`}>
                  <a>
                    <img
                      src="/assets/images/myt8-concession.jpeg"
                      alt=""
                    />
                    <div className={styles["link-text"]}>
                      <h2>{t("employedAtT8")}</h2>
                      <h3>{t("letsConnect")}</h3>
                      <p className="link-icon"></p>
                    </div>
                  </a>
                </div>
              </div>
            </React.Fragment>
          </div>
        </section>
      </LinkWithImageComponent>
      <section className="zigzag-section zigzag-width contact-form">
        <div className="container">
          {myT8Stories?.items?.map((item, key) => {
            return (
              item && (
                <React.Fragment key={key}>
                  <Card
                    imageClass="col-6"
                    contentClass="col-6"
                    title={item.title}
                    content={item.content}
                    imageUrl={item?.image?.url}
                    alt={item?.image?.description}
                    isImageOnLeft={key % 2 == 0}
                    link={item?.url}
                  />
                </React.Fragment>
              )
            );
          })}
        </div>
      </section>
      <Accordion faqs={faqs} />
      <FeaturedNews featuredNews={featuredNews} v={v} />
    </>
  );
};

export default MyT8;
