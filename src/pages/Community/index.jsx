import Banner from "../../components/Banner";
import FeaturedNews from "../../components/FeaturedNews";
import Card from "../../components/Card";
import Features from "../../components/Features";
import React from "react";
const Community = ({
  featuredNews,
  communityPartners,
  communityHero,
  communityFeatures,
  v
}) => {
  const featuresContent =
    communityFeatures?.items[0]?.featuresCollection?.items?.map((item) => {
      return item && {
        title: item.title,
        link: item.link,
        imageUrl: item?.logo?.url,
        description: item?.logo?.description,
        content: item.description,
      };
    });

  return (
    <>
      <Banner sectionClass="inner-banner" details={communityHero} />

      <Features content={featuresContent} />

      <section className="zigzag-section zigzag-width">
        <div className="container">
          {communityPartners?.items?.map((item, key) => {
            return (
              item && <React.Fragment key={key}>
                <Card
                  imageClass="col-6"
                  contentClass="col-6"
                  title={item.title}
                  content={item.content}
                  imageUrl={item?.image?.url}
                  alt={item?.image?.description}
                  isImageOnLeft={key % 2 == 0}
                  link={item?.url}
                />
              </React.Fragment>
            );
          })}
        </div>
      </section>

      <FeaturedNews featuredNews={featuredNews} v={v}/>
    </>
  );
};

export default Community;
