import Banner from "../../components/Banner";
import Card from "../../components/Card";
import FeaturedNews from "../../components/FeaturedNews";
import Features from "../../components/Features";
import Slider from "react-slick";
import styles from "./About.module.scss";
import React from "react";
const sliderSettings = {
  dots: true,
  // centerMode: true,
  slidesToShow: 2,
  slidesToScroll: 1,
  prevArrow: false,
  nextArrow: false,
  infinite: false,
  responsive: [
    {
      breakpoint: 767,
      settings: {
        slidesToShow: 1,
        slidesToScroll: 1,
      },
    },
    // You can unslick at a given breakpoint now by adding:
    // settings: "unslick"
    // instead of a settings object
  ],
};

const About = ({ featuredNews, partners, aboutHero, aboutFeatures,aboutSliderImages, v }) => {
  const featuresContent =
    aboutFeatures?.items[0]?.featuresCollection?.items?.map((item) => {
      return item && {
        title: item.title,
        link: item.link,
        imageUrl: item.logo?.url,
        description: item.logo?.description,
        content: item.description,
      };
    });

  return (
    <>
      <Banner sectionClass="inner-banner" details={aboutHero} />
      <Features content={featuresContent} />

      <section className={styles["about-carousel"]}>
        <Slider {...sliderSettings} className="multi-carousel">
          {aboutSliderImages?.items?.map((item, index) => {
            return (
              item && <div className="item" key={index}>
                <img src={item?.image?.url} alt={item?.image?.description} />
              </div>
            );
          })}
        </Slider>
      </section>

      <section className="zigzag-section">
        <div className="container">
          {partners?.items?.map((item, key) => {
            return (
              item && <React.Fragment key={key}>
                <Card
                  imageClass="col-4"
                  contentClass="col-8"
                  title={item.title}
                  content={item.content}
                  imageUrl={item?.image?.url}
                  alt={item?.image?.description}
                  isImageOnLeft={key % 2 == 0}
                  link={item?.url}
                />
              </React.Fragment>
            );
          })}
        </div>
      </section>
      <FeaturedNews featuredNews={featuredNews} v={v} />
    </>
  );
};

export default About;
