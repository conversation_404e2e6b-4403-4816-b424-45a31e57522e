.features {
  padding: 60px 0;
  background: url(/assets/images/shadow-bottom.png) no-repeat center top;
  display: flex;

  .col {
    border-left: solid 1px var(--gray-lte);
    padding: 0 15px;
    float: left;
    position: relative;
    text-align: center;
    h3 {
      font-size: 17px;
    }

    &:first-child {
      border: none;
      img {
        height: 36px;
        object-fit: contain;
      }
    }
  }

  .icon {
    min-width: 42px;
    margin: 0 10px 2px 0;
    float: left;
    img {
      height: 40px;
      object-fit: contain;
    }
  }

  h3 {
    margin: 0;
    color: var(--secondary);
    text-align: left;
  }

  p {
    text-align: center;
    font-size: 14px;
    font-family: Arial, Helvetica, sans-serif;
    margin: 20px 0 0 0;
  }

  @media (min-width: 768px) {
    .flex {
      display: inline-block;
      vertical-align: middle;
      min-width: 110px;
    }
  }
}

.opportunities {
  margin: 0;
  display: flex;
  flex-wrap: wrap;

  .col {
    padding: 0;
    float: left;
    position: relative;
    overflow: hidden;
    img {
      object-fit: cover;
      height: 100%;
      width: 80%;
    }

    &:after {
      content: '';
      display: block;
      width: 100%;
      height: 100%;
      position: absolute;
      top: 0;
      left: 0;
      background: rgb(248, 248, 248);
      background: linear-gradient(
        90deg,
        rgba(248, 248, 248, 0) 0%,
        rgba(248, 248, 248, 1) 70%
      );
    }
  }
}

.link-text {
  position: absolute;
  right: 20px;
  top: 0;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  max-width: 220px;
  z-index: 1;

  h2 {
    font-weight: 400;
  }

  margin: 0;

  p {
    margin: 0 0 0 40px;
  }
}

.opportunities .col:hover .link-text p {
  margin: 0 0 0 58px;
}

@media (max-width: 991px) {
  .opportunities {
    .col h2 {
      font-size: 20px;
    }

    .link-text {
      max-width: 170px;
    }

    .col .link-text p {
      margin: 0 0 0 20px;
      width: 20px;
      height: 15px;
    }
  }
}

@media screen and (max-width: 767px) {
  .features {
    background-size: contain;
    padding: 30px 10px;
    flex-direction: column;

    .col {
      width: 100%;
      flex-direction: column;
      align-items: flex-start;
      margin: 0;
      padding: 20px 0;
      border: none;
      border-top: solid 1px var(--gray-lte);
    }

    h3 {
      margin: 5px 0;
    }

    p {
      text-align: left;
      margin: 0 0 0 52px;
    }
  }

  .opportunities {
    margin: 0 -20px;
    background-color: #f8f8f8;

    .link-text {
      h2 {
        font-size: 18px;
      }

      p {
        width: 20px;
        height: 12px;
        margin: 0 0 0 20px;
      }
    }

    .col {
      width: 100%;
    }
  }
}
