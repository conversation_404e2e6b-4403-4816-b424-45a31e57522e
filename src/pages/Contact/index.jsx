import Link from "next/link";
import React, { useState, useEffect } from "react";
import { useContext } from "react";
import Banner from "../../components/Banner";
import ContactUsForm from "../../components/Forms/ContactUs";
import styles from "./Contact.module.scss";
import EmailUpdates from "../../components/EmailUpdates";
import EmailUpdatesForm from "../../components/Forms/EmailUpdates";
import EmailUpdatesContext from "../../../context/EmailUpdates/EmailUpdatesContext";
import { documentToReactComponents } from "@contentful/rich-text-react-renderer";
const Contact = ({ contactHero, opportunitesSection, stayUptoDateText, t , v, m, locale}) => {
  const [showResponse, setShowResponse] = useState(false);
  const { stayUpToDate, setStayUptoDate, setEmailAddress } =
    useContext(EmailUpdatesContext);

  useEffect(() => {
    setEmailAddress("");
    setStayUptoDate(false);
  }, [setEmailAddress, setStayUptoDate]);

  return (
    <>
      <Banner sectionClass="inner-banner" details={contactHero} />
      {stayUpToDate && <EmailUpdatesForm m={m} v={v} locale={locale} />}
      
      {!stayUpToDate && <EmailUpdates stayUptoDateText={stayUptoDateText} />}

      {!showResponse && !stayUpToDate && (
        <>
          <section className="contact-info contact-form">
            <div className="container">
              <div className={`row ${styles["opportunities"]}`}>
                {opportunitesSection?.items?.map((item, key) => {
                  return (
                    item && <React.Fragment key={key}>
                      <div className={`${styles["col"]} col-6`}>
                        <Link href={item?.url}>
                          <a href="#">
                            <img src={item?.image?.url} alt={item?.image?.description} />
                            <div className={styles["link-text"]}>
                              {item?.subContent?.json &&
                                documentToReactComponents(item?.subContent?.json)}
                              <p className="link-icon"></p>
                            </div>
                          </a>
                        </Link>
                      </div>
                    </React.Fragment>
                  );
                })}
              </div>
            </div>
          </section>
          <ContactUsForm onSetShowResponse={setShowResponse} t={t} v={v} locale={locale}/>
        </>
      )}

      {showResponse && !stayUpToDate && (
        <div className="form-section">
          <div className="container">
            <div className="form-text">
              <h3>{v("thankyou")}</h3>
              <p>
               {v("CONTACT_US_MSG")}
              </p>
            </div>
          </div>
        </div>
      )}
    </>
  );
};

export default Contact;
