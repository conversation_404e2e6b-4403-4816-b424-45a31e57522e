import Banner from "../../components/Banner";
import elasticlunr from "elasticlunr";
import { useState, useEffect } from "react";
import React from "react";
import styles from "./events.module.scss"
import News from "../../components/News";

const index = elasticlunr(function () {
  this.addField("title");
  this.addField("description");
  this.addField("eventImage");
  this.addField("eventStartDate");
  this.addField("eventEndDate");
  this.addField("eventLink");
  this.addField("outReachVideo");
  this.addField("createdAt");
  this.addField("updatedAt");
  this.setRef("id");

});

const indexPast = elasticlunr(function () {
  this.addField("title");
  this.addField("description");
  this.addField("eventImage");
  this.addField("eventStartDate");
  this.addField("eventEndDate");
  this.addField("eventLink");
  this.addField("outReachVideo");
  this.addField("createdAt");
  this.addField("updatedAt");
  this.setRef("id");
});

var dateOptions = { year: "numeric", month: "long", day: "numeric" };

const Events = ({ eventsHero, events, v }) => {


  const [searchQuery, setSearchQuery] = useState("");
  const [open, setOpen] = useState(false);
  const [caption, setCaption] = useState(v("tags.all"));


  const handleCaptionClick = () => {
    setOpen((open) => !open);
  };

  const handleItemClick = (e) => {
    setCaption(e.target.innerHTML);
    setOpen(false);
  };

  const getCurrentEvents = () => {
    return events?.filter(x => {
      var now = new Date();
      // now = new Date(now.getTime() + now.getTimezoneOffset() * 60000)
      return new Date(x.date) >= now;
    })
  }

  const getCompletedEvents = () => {
    return events?.filter(x => {
      var now = new Date();
      // now = new Date(now.getTime() + now.getTimezoneOffset() * 60000)
      return new Date(x.date) < now;
    })
  }

  // console.log(events)
  // eventBriteEvents?.events?.forEach((event) => {
  //   index.addDoc({
  //     id: event?.id,
  //     title: event?.name?.text,
  //     description: event?.description?.text,
  //     eventLink: event.url,
  //     eventImage: event?.logo?.url,
  //     eventStartDate: new Date(event?.start?.local),
  //     eventEndDate: new Date(event?.end?.local),
  //     createdAt: event.created,
  //     updatedAt: event.published,
  //   });
  // });
  getCurrentEvents().forEach((event) => {
    var eventDate = new Date(event?.date);
    var utc = new Date(eventDate.getTime() + eventDate.getTimezoneOffset() * 60000);
    index.addDoc({
      id: event?.sys.id,
      title: event?.title,
      description: event?.description,
      eventLink: event.eventLink,
      eventImage: event?.eventImage?.url,
      eventStartDate: utc,
      createdAt: event.firstPublishedAt,
      updatedAt: event.publishedAt,
      outReachVideo: event.outreachVideo
    });
  });
  var currentarr = [];
  Object.keys(index.documentStore.docs).map((doc) =>
    currentarr.push(index.documentStore.docs[doc])
  );
  currentarr = currentarr.sort(function (a, b) {
    return new Date(a.eventStartDate) - new Date(b.eventStartDate);
  });

  const [staticEvents, setStaticEvents] = useState(currentarr);

  getCompletedEvents().forEach((event) => {
    var eventDate = new Date(event?.date);
    var utc = new Date(eventDate.getTime() + eventDate.getTimezoneOffset() * 60000);
    indexPast.addDoc({
      id: event?.sys.id,
      title: event?.title,
      description: event?.description,
      eventLink: event.eventLink,
      eventImage: event?.eventImage?.url,
      eventStartDate: utc,
      createdAt: event.firstPublishedAt,
      updatedAt: event.publishedAt,
      outReachVideo: event.outreachVideo
    });
  });
  var completedarr = [];
  Object.keys(indexPast.documentStore.docs).map((doc) =>
    completedarr.push(indexPast.documentStore.docs[doc])
  );
  completedarr = completedarr.sort(function (a, b) {
    return new Date(a.eventStartDate) - new Date(b.eventStartDate);
  });

  const [pastEvents, setPastEvents] = useState(completedarr);

  useEffect(() => {
    search();
  }, []);



  const handleSearch = (e) => {
    e.preventDefault();
    search();
  };

  // // Divide the events 4 per row as per design
  // const eventChunks = staticEvents?.reduce((all, one, i) => {
  //   const ch = Math.floor(i / 4);
  //   all[ch] = [].concat(all[ch] || [], one);
  //   return all;
  // }, []);

  const search = () => {
    var currentarr = [];
    var pastarr = [];
    if (searchQuery) {
      currentarr = index
        .search(searchQuery, {
          fields: {
            title: { boost: 2 },
            body: { boost: 1 },
          },
        })
        .map((item) => {
          return index.documentStore.docs[item.ref];
        });

      pastarr = indexPast
        .search(searchQuery, {
          fields: {
            title: { boost: 2 },
            body: { boost: 1 },
          },
        })
        .map((item) => {
          return indexPast.documentStore.docs[item.ref];
        });


    } else {
      Object.keys(index.documentStore.docs).map((doc) => {
        currentarr.push(index.documentStore.docs[doc]);
      });

      Object.keys(indexPast.documentStore.docs).map((doc) => {
        pastarr.push(indexPast.documentStore.docs[doc]);
      });
    }
    currentarr = currentarr.sort(function (a, b) {
      return new Date(a.eventStartDate) - new Date(b.eventStartDate);
    });

    pastarr = pastarr.sort(function (a, b) {
      return new Date(b.eventStartDate) - new Date(a.eventStartDate);
    });
    setStaticEvents(currentarr);
    setPastEvents(pastarr)
  };

  const toMonthShortFormat = (date) => {
    const monthNames = ["Jan", "Feb", "Mar", "Apr",
      "May", "Jun", "Jul", "Aug",
      "Sep", "Oct", "Nov", "Dec"];

    const monthIndex = date.getMonth();
    return monthNames[monthIndex].toUpperCase();
  }

  function formatAMPM(date) {
    var hours = date.getHours();
    var minutes = date.getMinutes();
    var ampm = hours >= 12 ? 'PM' : 'AM';
    hours = hours % 12;
    hours = hours ? hours : 12; // the hour '0' should be '12'
    minutes = minutes < 10 ? '0' + minutes : minutes;
    var strTime = hours + ':' + minutes + ' ' + ampm;
    return strTime;
  }


  return (
    <>
      <Banner sectionClass="inner-banner" details={eventsHero} />

      <div className="filter-section">
        <div className="container">
          <div className="row">
            <div className="col col-6">
              <div className="left-part">
                <h2>{v("sortby")}</h2>
                <div className={`custom-dropdown ${open ? "open" : ""}`}>
                  <div className="caption" onClick={handleCaptionClick}>
                    {caption}
                  </div>
                  <div className="list">
                    <div className="item" onClick={handleItemClick}>
                      {v("tags.all")}
                    </div>
                    <div className="item" onClick={handleItemClick}>
                      {v("tags.upcomingEvents")}
                    </div>
                    <div className="item" onClick={handleItemClick}>
                      {v("tags.pastEvents")}
                    </div>

                  </div>
                </div>
              </div>
            </div>
            <div className="col col-6">
              <form className="flex search-form">
                <input
                  type="text"
                  placeholder={v("searchEvents")}
                  className="form-control"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                />
                <input
                  type="submit"
                  value={v("search")}
                  className="btn btn-secondary"
                  onClick={handleSearch}
                />
              </form>
            </div>
          </div>
        </div>
      </div>

      <section className={styles.eventsSection}>
        <div className="container">
          <div className="">
            {(caption == v("tags.all") || caption == v("tags.upcomingEvents")) && staticEvents?.length > 0 ? (
              staticEvents?.map((event, key) => {
                return (
                  event && <React.Fragment key={key}>
                    <div className={styles.events}>
                      <div className={styles.eventsItem}>
                        <div className="row">
                          <div className="col col-5">
                            <div className={`${styles.eventFlex} flex`}>
                              <div className={styles.date}>
                                <h3>{toMonthShortFormat(event.eventStartDate)}</h3>
                                <h2>{event.eventStartDate.getDate()}</h2>
                              </div>
                              <div className={styles.img}>
                                <a href={event.eventLink} target="_blank" rel="noreferrer">
                                  <img src={event.eventImage} alt="" />
                                </a>
                              </div>
                            </div>
                          </div>
                          <div className="col col-7">
                            <div className={styles.info}>
                              <h3>
                                <a href={event.eventLink} target="_blank" rel="noreferrer">
                                  {event.title}
                                </a>
                              </h3>
                              <h4>{formatAMPM(event.eventStartDate)} - {event.eventStartDate.toLocaleString("en-US", dateOptions)}</h4>
                              <p>
                                {event.description}
                              </p>
                              <div className={styles.action}>
                                <a href={event.eventLink} target="_blank" className="item-link" rel="noreferrer">
                                  <span className="link">View Event Details</span>
                                  <img
                                    src="/assets/images/icon-arrow.png"
                                    alt="icon-arrow"
                                  />
                                </a>
                              </div>
                             
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </React.Fragment>
                );
              })
            ) : (
              <>
                {caption == v("tags.upcomingEvents") &&
                  <div className="form-section">
                    <div className="container">
                      <div className="form-text">
                        <h3>{v("noentries")}</h3>
                      </div>
                    </div>
                  </div>
                }
              </>
            )}

            {(caption == v("tags.all") || caption == v("tags.pastEvents")) && pastEvents?.length > 0 ? (
              <>
                {caption == v("tags.all") && <div className="title flex-space-between"><h2>Past Events</h2></div>}
                {
                  pastEvents?.map((event, key) => {
                    return (
                      event && <React.Fragment key={key}>
                        <div className={styles.events}>
                          <div className={styles.eventsItem}>
                            <div className="row">
                              <div className="col col-5">
                                <div className={`${styles.eventFlex} flex`}>
                                  <div className={styles.date}>
                                    <h3>{toMonthShortFormat(event.eventStartDate)}</h3>
                                    <h2>{event.eventStartDate.getDate()}</h2>
                                  </div>
                                  <div className={styles.img}>
                                    <a href={event.eventLink} target="_blank" rel="noreferrer">
                                      <img src={event.eventImage} alt="" />
                                    </a>
                                  </div>
                                </div>
                              </div>
                              <div className="col col-7">
                                <div className={styles.info}>
                                  <h3>
                                    <a href={event.eventLink} target="_blank" rel="noreferrer">
                                      {event.title}
                                    </a>
                                  </h3>
                                  <h4>{formatAMPM(event.eventStartDate)} - {event.eventStartDate.toLocaleString("en-US", dateOptions)}</h4>
                                  <p>
                                    {event.description}
                                  </p>
                                  <div className={styles.action}>
                                    <a href={event.eventLink} target="_blank" className="item-link" rel="noreferrer">
                                      <span className="link">View Event Details</span>
                                      <img
                                        src="/assets/images/icon-arrow.png"
                                        alt="icon-arrow"
                                      />
                                    </a>
                                  </div>
                                  {event.outReachVideo && <div className={styles.action}>
                                    <a href={event.outReachVideo} target="_blank" className="item-link" rel="noreferrer">
                                      <span className="link">Watch Outreach Video</span>
                                      <img
                                        src="/assets/images/icon-arrow.png"
                                        alt="icon-arrow"
                                      />
                                    </a>
                                  </div>}
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </React.Fragment>
                    );
                  })
                }
              </>

            ) : (
              <>
                {caption == v("tags.pastEvents") &&
                  <div className="form-section">
                    <div className="container">
                      <div className="form-text">
                        <h3>{v("noentries")}</h3>
                      </div>
                    </div>
                  </div>
                }
              </>
            )}
          </div>
        </div>
      </section>
    </>
  );

};

export default Events;
