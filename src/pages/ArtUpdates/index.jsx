import React, { useState, useEffect } from "react";
import Banner from "../../components/Banner";
import News from "../../components/News";
import elasticlunr from "elasticlunr";

const index = elasticlunr(function () {
  this.addField("artTitle");
  this.addField("artBlogImage");
  this.addField("artTags");
  this.addField("artSlug");
  this.addField("artCreatedAt");
  this.addField("artUpdatedAt");
  this.setRef("artId");
});

const ArtUpdates = ({ updatesHero, blogs , v}) => {
 
  const [searchQuery, setSearchQuery] = useState("");
  const [caption, setCaption] = useState(v("tags.latestnews"));

  blogs?.forEach((blog) => {
   
    index.addDoc({
      artId: blog.sys.id,
      artTitle: blog.title,
      artTags: blog.tags,
      secondaryTags: blog?.secondaryTags || [],
      artSlug: blog.slug,
      artBlogImage: blog?.blogImage?.url,
      artCreatedAt: blog.sys.firstPublishedAt,
      artUpdatedAt: blog.sys.publishedAt,
    });
  });
  var arr = [];
  Object.keys(index.documentStore.docs).map((doc) =>
    arr.push(index.documentStore.docs[doc])
  );
  arr = arr.sort(function (a, b) {
    return new Date(b.updatedAt) - new Date(a.updatedAt);
  });

  const [staticBlogs, setStaticBlogs] = useState(arr);

  useEffect(() => {
   
    setStaticBlogs(arr);
  }, []);

  const handleSearch = (e) => {
    e.preventDefault();
    search();
  };

  const handleDocumentKeyUp = (event) => {
    if ((event.keyCode || event.event) === 27) {
      setOpen(false);
    }
  };

  // Divide the blogs 4 per row as per design
  const blogChunks = staticBlogs?.reduce((all, one, i) => {
    const ch = Math.floor(i / 4);
    all[ch] = [].concat(all[ch] || [], one);
    return all;
  }, []);

  const handleDocumentClick = (event) => {
    if (!event.target.closest(".custom-dropdown > .caption")) {
      setOpen(false);
    }
  };

  useEffect(() => {
    document.addEventListener("keyup", handleDocumentKeyUp);
    document.addEventListener("click", handleDocumentClick);
    return () => {
      document.removeEventListener("keyup", handleDocumentKeyUp);
      document.removeEventListener("click", handleDocumentClick);
    };
  }, []);

  useEffect(() => {
    search();
  },[caption])

  const [open, setOpen] = useState(false);

  const handleCaptionClick = () => {
    setOpen((open) => !open);
  };

  const handleItemClick = (e) => {
    setCaption(e.target.innerHTML);
    setOpen(false);
  };

  const search = () => {
    var arr = [];
    if (searchQuery) {
      arr = index
        .search(searchQuery, {
          fields: {
            artTitle: { boost: 2 },
            body: { boost: 1 },
          },
        })
        .map((item) => {
          return index.documentStore.docs[item.ref];
        });
    } else {
      Object.keys(index.documentStore.docs).map((doc) => {
        arr.push(index.documentStore.docs[doc]);
      });
    }
    if (caption && caption != v("tags.latestnews")) {
      arr = arr.filter(function (item) {
        return item.artTags == caption || item.secondaryTags.includes(caption);
      });
    }
    arr = arr.sort(function (a, b) {
      return new Date(b.updatedAt) - new Date(a.updatedAt);
    });
    setStaticBlogs(arr);
  };

  return (
    <>
      <Banner sectionClass="inner-banner" details={updatesHero} />
        <div className="filter-section">
          <div className="container">
            <div className="row">
              <div className="col col-6">
                <div className="left-part">
                  <h2>{v("sortby")}</h2>
                  <div className={`custom-dropdown ${open ? "open" : ""}`}>
                    <div className="caption" onClick={handleCaptionClick}>
                      {caption}
                    </div>
                    <div className="list">
                      <div className="item" onClick={handleItemClick}>
                      {v("tags.latestnews")}
                      </div>
                      <div className="item" onClick={handleItemClick}>
                      {v("tags.businessOpportunities")}
                      </div>
                      <div className="item" onClick={handleItemClick}>
                      {v("tags.community")}
                      </div>
                      <div className="item" onClick={handleItemClick}>
                      {v("tags.employmentOpportunities")}
                      </div>
                      <div className="item" onClick={handleItemClick}>
                      {v("tags.events")}
                      </div>
                      <div className="item" onClick={handleItemClick}>
                      {v("tags.news")}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div className="col col-6">
                <form className="flex search-form">
                  <input
                    type="text"
                    placeholder={v("searchNews")}
                    className="form-control"
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                  />
                  <input
                    type="submit"
                    value={v("search")}
                    className="btn btn-secondary"
                    onClick={handleSearch}
                  />
                </form>
              </div>
            </div>
          </div>
        </div>

        <section className="updates-section news-section">
          <div className="container">
            <div className="">
              {blogChunks?.length > 0 ? (
                blogChunks?.map((blog, key) => {
                  return (
                    <React.Fragment key={key}>
                      <div className="row news">
                        {blog.map((item, index) => {
                          return (
                            <React.Fragment key={index}>
                              <News
                                key={index}
                                imageUrl={item.artBlogImage}
                                title={item.artTags}
                                content={item.artTitle}
                                url={`/blog/${item.artSlug}/`}
                              />
                            </React.Fragment>
                          );
                        })}
                      </div>
                    </React.Fragment>
                  );
                })
              ) : (
                <>
                  <div className="form-section">
                    <div className="container">
                      <div className="form-text">
                        <h3>{v("noentries")}</h3>
                      </div>
                    </div>
                  </div>
                </>
              )}
            </div>
          </div>
        </section>
    </>
  );
};

export default ArtUpdates;
