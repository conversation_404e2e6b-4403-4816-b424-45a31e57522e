import FeaturedNews from "../../components/FeaturedNews";
import Jobs from "../../components/Jobs";
import EmailUpdates from "../../components/EmailUpdates";
import EmailUpdatesForm from "../../components/Forms/EmailUpdates";
import EmailUpdatesContext from "../../../context/EmailUpdates/EmailUpdatesContext";
import { useContext, useEffect } from "react";

import Events from "../../components/Event";
const Home = ({ featuredNews, homeFeaturesSection, opportunitesSection, stayUptoDateText,events, m, v, locale , l}) => {

  const { stayUpToDate, setStayUptoDate, setEmailAddress } =
    useContext(EmailUpdatesContext);

  useEffect(() => {
    setEmailAddress("");
    setStayUptoDate(false);
  }, [setEmailAddress, setStayUptoDate]);

  return (
    <>
      {stayUpToDate && <EmailUpdatesForm m={m} v={v} locale={locale}/>}
      {!stayUpToDate && (
        <>
          <EmailUpdates stayUptoDateText={stayUptoDateText} />
          <Jobs
            featuresSection={homeFeaturesSection}
            opportunitesSection={opportunitesSection}
          />

          <FeaturedNews featuredNews={featuredNews}  v={v} />
          <Events events={events} v={v} l={l}/>

        </>
      )}
    </>
  );
};

export default Home;
