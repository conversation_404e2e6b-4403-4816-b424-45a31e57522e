import React, { useState } from "react";
import Banner from "../../components/Banner";
import FeaturedNews from "../../components/FeaturedNews";
import EmploymentOpportunitiesForm from "../../components/Forms/EmploymentOpportunities";
import { EMPLOYMENT_OPPORTUNITY_MSG } from "../../constants";

const EmploymentOpportunities = ({
  featuredNews,
  employmentoppHero,
  textSection,
  t,
  v,
  locale
}) => {
  const [showResponse, setShowResponse] = useState(false);

  const handleClick = () => {
    setShowResponse(false);
  }

  return (
    <>
      <Banner sectionClass="inner-banner" details={employmentoppHero} />
      {!showResponse && (
        <>
          <section className="two-text-section">
            <div className="container">
              <div className="row">
                {textSection?.items?.map((item, key) => {
                  return (
                    item && <React.Fragment key={key}>
                      <div className="col col-6">
                        <div className="custom-title flex align-center flex-direction-row">
                          <h4 className="m-0">{item.title}</h4>
                        </div>
                        <p>{item.description}</p>
                      </div>
                    </React.Fragment>
                  );
                })}
              </div>
            </div>
          </section>

          <EmploymentOpportunitiesForm onSetShowResponse={setShowResponse} t={t} v={v} locale={locale}/>

          <FeaturedNews featuredNews={featuredNews} v={v} />
        </>
      )}

      {showResponse && (
        <>
          <div className="form-section form-opportunities">
            <div className="container">
              <div className="form-text">
                <h3>{v("thankyou")}</h3>
                <p>{v("EMPLOYMENT_OPPORTUNITY_MSG")}</p>
                <input
                  type="button"
                  className="btn btn-primary"
                  value={v("backtoemployment")}
                  onClick={handleClick}
                ></input>
              </div>
            </div>
          </div>
        </>
      )}
    </>
  );
};

export default EmploymentOpportunities;
