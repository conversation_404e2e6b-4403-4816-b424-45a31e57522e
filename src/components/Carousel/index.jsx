import Slider from "react-slick";
import Link from "next/link";
import LinkComponent from "../../../internationalization/Link";
import { documentToReactComponents } from "@contentful/rich-text-react-renderer";
const sliderSettings = {
  prevArrow: false,
  nextArrow: false,
  dots: true,
  infinite: true,
  autoplay: true,
  autoplaySpeed: 8000,
  slidesToShow: 1,
};

const Carousel = ({ heroImages }) => {
  return (
    <section className="carousel-section">
      <Slider {...sliderSettings} className="carousel">
        {heroImages?.items?.map((item, key) => {
          return (
            item && <div className="item" key={key}>
              <img className="center-align" src={item?.heroImage?.url} alt={item?.heroImage?.description} />
              {/* <img
                className="center-align mobile"
                src={item.heroImage.url}
                alt=""
              /> */}
              <div className="container">
                <div className="carousel-content">
                  <h2>{documentToReactComponents(item.title.json)}</h2>
                  <p>{item.description}</p>
                  <p>
                    {item?.url && <LinkComponent
                      href={item?.url}
                      className="link-icon"
                    ></LinkComponent>}
                  </p>
                </div>
              </div>
            </div>
          );
        })}
      </Slider>
    </section>
  );
};

export default Carousel;
