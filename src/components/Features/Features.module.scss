.features-box {
  padding: 60px 0;
  display: flex;

  .col {
    border-left: solid 1px var(--gray-lte);
    padding: 0 15px;
    float: left;
    position: relative;
    text-align: center;
    h3 {
      font-size: 17px;
    }

    &:first-child {
      border: none;
      img {
        height: 36px;
        object-fit: contain;
      }
    }
  }

  .icon {
    min-width: 42px;
    margin: 0 10px 2px 0;
    float: left;
    img {
      height: 40px;
      object-fit: contain;
    }
  }

  h3 {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
    color: #2170c5;
    text-align: left;
  }

  p {
    color: #48586b;
    font-size: 14px;
    margin: 20px 0 0;
  }

  @media (min-width: 768px) {
    .flex {
      display: inline-block;
      vertical-align: middle;
      min-width: 110px;
    }
  }
}

@media screen and (max-width: 767px) {
  .col {
    width: 100%;
  }

  .features-box {
    background-size: contain;
    padding: 30px 10px;
    flex-direction: column;

    .col {
      width: 100%;
      flex-direction: column;
      align-items: flex-start;
      margin: 0;
      padding: 20px 0;
      border: none;
      border-top: solid 1px var(--gray-lte);
    }

    h3 {
      margin: 5px 0;
    }

    p {
      text-align: left;
      margin: 0 0 0 52px;
    }
  }
}
