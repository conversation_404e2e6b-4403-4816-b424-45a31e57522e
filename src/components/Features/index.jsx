import Link from "next/link";
import LinkComponent from "../../../internationalization/Link";
import React from "react";
import styles from "./Features.module.scss";

const isUrlAbsolute = (url) =>
  url.indexOf("//") === 0
    ? true
    : url.indexOf("://") === -1
    ? false
    : url.indexOf(".") === -1
    ? false
    : url.indexOf("/") === -1
    ? false
    : url.indexOf(":") > url.indexOf("/")
    ? false
    : url.indexOf("://") < url.indexOf(".")
    ? true
    : false;

const Features = ({ content }) => {
  return (
    <section>
      <div className="container">
        <div className={`row ${styles["features-box"]}`}>
          {content?.map((item, key) => {
            return (
              item && <React.Fragment key={key}>
                <div className={`${styles["col"]} col-4`}>
                  <div className={styles.flex}>
                    <div className={styles.icon}>
                      <img src={item?.imageUrl} alt={item?.description} />
                    </div>
                    <h3>
                      {item?.link && isUrlAbsolute(item?.link) ? (
                        <>
                          <Link href={item?.link}>
                            <a target="_blank">{item?.title}</a>
                          </Link>
                        </>
                      ) : (
                        <>
                          {item?.link && <LinkComponent
                            href={item?.link}
                            linktext={item?.title}
                          ></LinkComponent>}
                        </>
                      )}
                    </h3>
                  </div>
                  <p>{item?.content}</p>
                </div>
              </React.Fragment>
            );
          })}
        </div>
      </div>
    </section>
  );
};

export default Features;
