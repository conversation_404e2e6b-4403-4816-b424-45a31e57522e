import LinkComponent from "../../../internationalization/Link";
import LinkWithImage from "../../../internationalization/LinkWithImage";
import Link from "next/link";
import styles from "./Header.module.scss";
import { useRouter } from "next/router";
import React from "react";
const Header = ({ topPartnerLogos, navLinksText }) => {
  const router = useRouter();
  // const handleClick = (e, path) => {
  //   e.preventDefault();
  //   document.querySelector("body").classList.toggle(styles["on"]);
  //   document
  //     .getElementById("opportunities-sub")
  //     .classList.remove(styles["open"]);
  //   path && router.push(path);
  // };

  const handleClick = (e) => {
    // e.preventDefault();
    document.querySelector("body").classList.toggle(styles["on"]);
    document
      .getElementById("opportunities-sub")
      .classList.remove(styles["open"]);
  };

  const handleSubMenuClick = (e) => {
    e.preventDefault();
    document
      .getElementById("opportunities-sub")
      .classList.toggle(styles["open"]);
  };

  return (
    <>
      <header className={styles.header}>
        <div className={styles["header-top"]}>
          <div className="container flex-center-end">
            {topPartnerLogos?.items[0]?.logosCollection?.items?.map(
              (item, key) => {
                return (
                  item && <React.Fragment key={key}>
                    <a href={item?.url} target="_blank" rel="noreferrer">
                      <img src={item?.logo?.url} width={item?.logoWidth} alt={item?.logo?.description} />
                    </a>
                  </React.Fragment>
                );
              }
            )}
          </div>
        </div>

        <div className={styles["header-action"]}>
          <div className="container flex">
            <div className={styles.logo}>
              <LinkWithImage href="/">
                <img src="/assets/images/jfkt8-logo.png" alt="logo" />
              </LinkWithImage>
            </div>

            <a
              href="#"
              className={`${styles["menu-toggle"]}`}
              onClick={handleClick}
            >
              <span></span>
            </a>
            <div className={`${styles["menu-action"]} ${"noprint"}`}>
              <ul className={styles.menu}>
                <li
                  className={router.pathname.indexOf("/about") != -1 ? styles.active : ""}
                >
                  <LinkComponent
                    href="/about/"
                    onClick={(e) => handleClick(e)}
                    linktext={navLinksText.about}
                  ></LinkComponent>
                  {/* <Link href="/">
                    <a onClick={(e) => handleClick(e, "/about")}>{navLinksText.about}</a>
                  </Link> */}
                </li>

                <li className={styles.sub} id="opportunities-sub">
                  <a href="#" onClick={handleSubMenuClick}>
                    {navLinksText.opportunities}
                  </a>
                  <div className={styles["sub-menu"]}>
                    <ul>
                      <li
                        className={
                          router.pathname.indexOf("/business-opportunities") != -1
                            ? styles.active
                            : ""
                        }
                      >
                        <LinkComponent
                          href="/business-opportunities/"
                          onClick={(e) => handleClick(e)}
                          linktext={navLinksText.businessOpportunities}
                        ></LinkComponent>
                      </li>
                      <li

                      >
                        <LinkComponent
                          href="https://forms.office.com/pages/responsepage.aspx?id=9wSZCNL4qUKHkfbgEApIL_0q2d2rvDJLtemEvaCTGyhUQTNLOTRRQlZERUlYR0FEVlBYTEJMNks4WC4u"
                          onClick={(e) => handleClick(e)}
                          linktext={navLinksText.localBusinessAccelerater}
                          target="_blank"
                        ></LinkComponent>
                      </li>
                      <li
                        className={
                          router.pathname.indexOf("/employment-opportunities") != -1
                            ? styles.active
                            : ""
                        }
                      >
                        <LinkComponent
                          href="/employment-opportunities/"
                          onClick={(e) => handleClick(e)}
                          linktext={navLinksText.employmentOpportunities}
                        ></LinkComponent>
                      </li>
                    </ul>
                  </div>
                </li>
                <li
                  className={
                    router.pathname.indexOf("/community") != -1 ? styles.active : ""
                  }
                >
                  <LinkComponent
                    href="/community/"
                    onClick={(e) => handleClick(e)}
                    linktext={navLinksText.community}
                  ></LinkComponent>
                </li>
                <li className={router.pathname.indexOf("/news") != -1 ? styles.active : ""}>
                  <LinkComponent
                    href="/news/"
                    onClick={(e) => handleClick(e)}
                    linktext={navLinksText.news}
                  ></LinkComponent>
                </li>
                <li className={router.pathname.indexOf("/events") != -1 ? styles.active : ""}>
                  <LinkComponent
                    href="/events/"
                    onClick={(e) => handleClick(e)}
                    linktext={navLinksText.events}
                  ></LinkComponent>
                </li>
                <li className={router.pathname.indexOf("/art") != -1 ? styles.active : ""}>
                  <LinkComponent
                    href="/art/"
                    onClick={(e) => handleClick(e)}
                    linktext={navLinksText.art}
                  ></LinkComponent>
                </li>
                <li
                  className={router.pathname.indexOf("/contact") != -1 ? styles.active : ""}
                >
                  {/* <Link href="/">
                    <a onClick={(e) => handleClick(e, "/contact")}>
                      {navLinksText.contactus}
                    </a>
                  </Link> */}
                  <LinkComponent
                    href="/contact/"
                    onClick={(e) => handleClick(e)}
                    linktext={navLinksText.contactus}
                  ></LinkComponent>
                </li>
                <li
                  className={router.pathname.indexOf("/myt8") != -1 ? styles.active : ""}
                >

                  <LinkComponent
                    href="/myt8/"
                    onClick={(e) => handleClick(e)}
                    linktext={navLinksText.myT8}
                  ></LinkComponent>
                </li>
                <li>
                  <a href="https://www.jfkshopdine.com/" target="_blank" rel="noreferrer">{navLinksText.exploreCurrentTerminal}</a>
                </li>
              </ul>
            </div>
          </div>
        </div>
      </header>
    </>
  );
};

export default Header;
