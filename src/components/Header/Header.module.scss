.header {
  width: 100%;
  min-width: 290px;
  background-color: white;
}

.header-top {
  background: var(--gray) url(/assets/images/shadow-top.png) no-repeat center bottom;
  height: 42px;
  padding: 6px 0;

  div {
    height: 100%;
    a {
      margin-left: 30px;
      &:first-child {
        margin-top: -4px;
      }
    }
  }
}

.header-action {
  padding: 20px 0;
}

.logo {
  max-width: 170px;
  cursor: pointer;
  height: 50px;
  display: flex;
  align-items: center;
}

.menu-action {
  margin-left: auto;

  .btn {
    margin-left: 10px;
  }
}

.menu {
  list-style: none;
  margin: 0;
  padding: 0;
  display: flex;

  li {
    position: relative;
    margin-left: 20px;
    font-size: 14px;
    // @media screen and (min-width: 768px) and (max-width: 991px) {
    //   margin-left: 20px;
    // }
  }

  a {
    transition: all 0.3s ease;

    &:hover {
      color: var(--secondary);
    }
  }

  .active a {
    color: var(--secondary);
  }
}

.sub {
  > a {
    padding-right: 24px;
    &::after {
      content: '';
      position: absolute;
      right: 0;
      top: 1px;
      width: 20px;
      height: 20px;
      background: url(/assets/images/menu-arrow.svg) no-repeat right center;
      background-size: cover;
      transition: all 0.3s ease;
      cursor: pointer;
      opacity: 0.5;
    }
  }
}
.sub.open {
  > a::after {
    transform: rotate(180deg);
  }
  .sub-menu {
    max-height: 500px;
  }
}
.sub-menu {
  ul {
    padding: 8px;
    margin: 0;
    list-style: none;
  }
  li {
    margin: 0 0 1px;
    display: block;
    a {
      display: block;
      font-size: 14px;
      color: #6b7280;
      border-radius: 0.25rem;
      padding: 12px 15px;
      transition: color 0.1s ease;
      &:hover {
        background-color: #dbeafe;
        color: var(--secondary);
      }
    }
  }
  li.active {
    a {
      background-color: #dbeafe;
      color: var(--secondary);
    }
  }
}

@media screen and (min-width: 768px) {
  .sub-menu {
    position: absolute;
    top: 140%;
    left: 50%;
    background-color: #fff;
    box-shadow: inset 0 0 0 0.08333333rem #e5e7eb;
    border-radius: 0.5rem;
    z-index: 3;
    overflow: hidden;
    max-height: 0;
    transition: all 0.3s ease;
    width: 240px;
    margin-left: -120px;
  }
}

@media screen and (max-width: 1023px) {
  .mobile {
    display: block !important;
  }
  .sub {
    > a {
      &::after {
        top: 15px;
        right: 15px;
      }
    }
  }
  .menu-action {
    position: fixed;
    right: -300px;
    top: 115px;
    background-color: white;
    height: calc(100% - 115px);
    width: 100%;
    max-width: 300px;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
    z-index: -1;

    .btn {
      margin: 8px 16px;
    }
  }

  .menu-toggle {
    width: 26px;
    height: 26px;
    margin: 0 0 0 auto;
    padding: 13px 0 0 0;

    span {
      &:after,
      &:before {
        content: '';
        position: absolute;
        left: 0;
        top: -9px;
      }

      &:after {
        top: 9px;
        width: 100%;
        height: 3px;
        background-color: var(--primary);
        transition: all 0.3s;
        backface-visibility: hidden;
        border-radius: 2px;
      }

      position: relative;
      display: block;
      width: 100%;
      height: 3px;
      background-color: var(--primary);
      transition: all 0.3s;
      backface-visibility: hidden;
      border-radius: 2px;

      &:before {
        width: 100%;
        height: 3px;
        background-color: var(--primary);
        transition: all 0.3s;
        backface-visibility: hidden;
        border-radius: 2px;
      }
    }
  }

  .menu a {
    display: block;
    margin: 5px 0;
    text-align: center;
  }

  .menu-button {
    display: flex;
    flex-direction: column;
  }

  .menu {
    li {
      a {
        padding: 6px 0;

        &:hover {
          background-color: #dbeafe;
          color: var(--secondary);
        }
      }

      margin-left: 0;
      padding: 0;

      &:not(:last-child) {
        border-bottom: solid 1px #c0c0c0;
      }

      a {
        padding: 15px 10px;
        margin: 0;
      }
    }

    .sub-menu {
      position: relative;
      top: 0;
      left: 0;
      overflow: hidden;
      max-height: 0;
      transition: all 0.3s ease;
      ul {
        padding: 0 15px 15px;
      }
      li {
        border: none;
        a {
          padding: 10px 15px;
        }
      }
    }
    .sub {
      &::after {
        top: 16px;
        right: 16px;
      }
    }

    display: flex;
    flex-direction: column;
  }

  .on {
    overflow: hidden;

    .menu-toggle span {
      background-color: transparent;

      &:before {
        transform: rotate(45deg) translate(5px, 5px);
      }

      &:after {
        transform: rotate(-45deg) translate(7px, -8px);
      }
    }

    .menu-action {
      right: 0;
      z-index: 11;
      opacity: 1;
      visibility: visible;
    }
  }

  .logo {
    max-width: 140px;
    height: auto;
  }
}