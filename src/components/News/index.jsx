import styles from "./News.module.scss";
import LinkComponent from "../../../internationalization/Link";
import LinkWithImage from "../../../internationalization/LinkWithImage";
const News = ({ imageUrl, title, content, url }) => {
  return (
    <div className="col col-3">
      <div className={`news-img ${styles["news-img"]}`}>
        <LinkWithImage href={url ? url : "#"}>
          <a href="#">
            <img src={imageUrl} alt="" />
          </a>
        </LinkWithImage>

        {/* <a href={url ? url : "#"}>
          <img src={imageUrl} alt="" />
        </a> */}
      </div>
      <h3>
        <LinkComponent href={url ? url : "#"} linktext={title}></LinkComponent>
      </h3>
      <p>
        <LinkComponent
          href={url ? url : "#"}
          linktext={content}
        ></LinkComponent>
        {/* <a href={url ? url : "#"}> {content}</a> */}
      </p>
    </div>
  );
};

export default News;
