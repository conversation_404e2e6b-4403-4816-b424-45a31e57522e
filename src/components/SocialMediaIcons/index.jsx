const SocialMediaIcons = ({eventUrl = ""}) => {  
  function socialWindow(url) {
    var left = (screen.width - 570) / 2;
    var top = (screen.height - 570) / 2;

    var params =
      "menubar=no,toolbar=no,status=no,width=570,height=570,top=" +
      top +
      ",left=" +
      left;

    window.open(url, "New Window", params);
  }

  const isUrlAbsolute = (url) =>
  url.indexOf("//") === 0
    ? true
    : url.indexOf("://") === -1
    ? false
    : url.indexOf(".") === -1
    ? false
    : url.indexOf("/") === -1
    ? false
    : url.indexOf(":") > url.indexOf("/")
    ? false
    : url.indexOf("://") < url.indexOf(".")
    ? true
    : false;

  const handleFaceBookShare = () => {
    // var url = (eventUrl == "" ? document.URL : document.URL + eventUrl).replace(/([^:]\/)\/+/g, "$1"); 
    var url = (eventUrl == "" ? window.location.href : isUrlAbsolute(eventUrl) ? eventUrl : window.location.href + eventUrl).replace(/([^:]\/)\/+/g, "$1"); 
    url = "https://www.facebook.com/sharer.php?u=" + encodeURI(url);
    socialWindow(url);
  };

  const handleTwitterShare = () => {
    var url = (eventUrl == "" ? window.location.href : isUrlAbsolute(eventUrl) ? eventUrl : window.location.href + eventUrl).replace(/([^:]\/)\/+/g, "$1"); 
    // var url = (eventUrl == "" ? document.URL : document.URL + eventUrl).replace(/([^:]\/)\/+/g, "$1"); 
    url = "https://twitter.com/intent/tweet?url=" + encodeURI(url);
    socialWindow(url);
  };

  const handleLinkedInShare = () => {
    var url = (eventUrl == "" ? window.location.href : isUrlAbsolute(eventUrl) ? eventUrl : window.location.href + eventUrl).replace(/([^:]\/)\/+/g, "$1"); 
    // var url = (eventUrl == "" ? document.URL : document.URL + eventUrl).replace(/([^:]\/)\/+/g, "$1"); 
    url = "https://www.linkedin.com/shareArticle?mini=true&url=" + encodeURI(url);
    socialWindow(url);
  };

  const handleGooglePlusShare = () => {
    // var url = (eventUrl == "" ? document.URL : document.URL + eventUrl).replace(/([^:]\/)\/+/g, "$1"); 
    var url = (eventUrl == "" ? window.location.href : isUrlAbsolute(eventUrl) ? eventUrl : window.location.href + eventUrl).replace(/([^:]\/)\/+/g, "$1"); 
    url = "https://plus.google.com/share?url=" + encodeURI(url);
    socialWindow(url);
  };

  const handlePintrestShare = () => {
    // var url = (eventUrl == "" ? document.URL : document.URL + eventUrl).replace(/([^:]\/)\/+/g, "$1"); 
    var url = (eventUrl == "" ? window.location.href : isUrlAbsolute(eventUrl) ? eventUrl : window.location.href + eventUrl).replace(/([^:]\/)\/+/g, "$1"); 
    url = "http://pinterest.com/pin/create/button/?url=" + encodeURI(url);
    socialWindow(url);
  };

  const handleBookmarkClick = () => {
    window.dispatchEvent(
      new KeyboardEvent("keydown", {
        key: "d",
        ctrlKey: true,
        code: "KeyD",
      })
    );
  };

  return (
    <div className="blog-social flex noprint">
      {/* <div className="bookmark" onClick={handleBookmarkClick} id="lnkBookmark">
        <a href="#">
          <img src="/assets/images/icon-bookmark.svg" alt="icon-bookmark" />
        </a>
      </div> */}
      <div className="print" onClick={() => window.print()}>
        <a href="#">
          <img src="/assets/images/icon-printer.svg" alt="icon-printer" />
        </a>
      </div>
      <div className="social">
        <span>
          <img src="/assets/images/icon-share.svg" alt="icon-share" />
        </span>
        <a onClick={handleLinkedInShare}>
          <img src="/assets/images/icon-linkedin.svg" alt="icon-linkedin" />
        </a>
        <a onClick={handleFaceBookShare}>
          <img src="/assets/images/icon-facebook.svg" alt="icon-facebook" />
        </a>
        <a onClick={handleTwitterShare}>
          <img src="/assets/images/icon-twitter.svg" alt="icon-twitter" />
        </a>
       {/*  <a href="#">
          <img src="/assets/images/icon-instagram.svg" alt="" />
        </a> */}
        <a onClick={handleGooglePlusShare}>
          <img src="/assets/images/icon-google.svg" alt="icon-google" />
        </a>
        <a onClick={handlePintrestShare}>
          <img src="/assets/images/icon-printrest.svg" alt="icon-pintrest" />
        </a>
      </div>
    </div>
  );
};

export default SocialMediaIcons;
