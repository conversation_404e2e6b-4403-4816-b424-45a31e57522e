import { Formik, Form, ErrorMessage, Field } from "formik";
import * as Yup from "yup";
import TextError from "../../TextError";
import toast from "../../Toast";
import { contactUs } from '../../../../api'
import { CONTACT_US_MSG, CONTACT_US } from '../../../constants'
import React, { useState } from "react";
import ReCAPTCHA from "react-google-recaptcha"
import { useRef } from "react";
const ContactUsForm = ({ t, v, locale }) => {

  const captchaRef = useRef(null);
  const [captchaValue, setCaptchaValue] = useState(null);
  const initialValues = {
    firstName: "",
    lastName: "",
    mobile: "",
    company: "",
    email: "",
    message: "",
    inquiryType: "",
    locale: locale
  };

  const inquiryOptions = [
    { key: 'general', value: 'General Inquiry' },
    { key: 'media', value: 'Media Inquiry' }
  ];

  const onChange = (value) => {
    setCaptchaValue(value);
  }

  const validationSchema = Yup.object({
    firstName: Yup.string().required(v("validations.firstName")),
    lastName: Yup.string().required(v("validations.lastName")),
    mobile: Yup.string().required(v("validations.mobileNumber")).matches(/^\d{10}$/, "Please enter a valid 10 digit number"),
    email: Yup.string().required(v("validations.email"))
      .email(v("validations.invalidEmail")),
    inquiryType: Yup.string().required("Inquiry type is required"),
    message: Yup.string(),
  });

  const handleSubmit = async (values, { resetForm }) => {
    try {
      if (captchaValue == null) {
        toast({ type: 'error', message: "Please check 'I am not robot'" })
        return false;
      } else {
        await contactUs(CONTACT_US, values)
        toast({ type: "success", message: v("CONTACT_US_MSG") });
        setCaptchaValue(null);
        captchaRef.current.reset();
        // onSetShowResponse(true);
        resetForm();
      }
    } catch (error) {
      toast({ type: 'error', message: v("wentwrong") })
    }
  };

  return (
    <>
      <Formik
        enableReinitialize={true}
        initialValues={initialValues}
        validationSchema={validationSchema}
        onSubmit={handleSubmit}
      >
        {({ handleChange, values, isSubmitting }) => {
          return (
            <Form>
              <section className="form-section contact-form">
                <div className="container">
                  <div className="form-text">
                    <h3>{t("getInTouch")}</h3>
                    <p>
                      {t("generalQuestion")}
                    </p>
                    <span>({t("required")})</span>
                  </div>
                  <div className="row">
                    <div className="col col-8">
                      <Field
                        id="firstName"
                        type="text"
                        value={values.firstName}
                        onChange={handleChange}
                        className="form-control"
                        placeholder={t("firstName")}
                      />
                      <ErrorMessage name="firstName" component={TextError} />

                      <Field
                        id="lastName"
                        type="text"
                        value={values.lastName}
                        onChange={handleChange}
                        className="form-control"
                        placeholder={t("lastName")}
                      />
                      <ErrorMessage name="lastName" component={TextError} />

                      <Field
                        id="mobile"
                        type="text"
                        value={values.mobile}
                        onChange={handleChange}
                        className="form-control"
                        placeholder={t("mobileNumber")}
                      />
                      <ErrorMessage name="mobile" component={TextError} />

                      <Field
                        id="company"
                        type="text"
                        value={values.company}
                        onChange={handleChange}
                        className="form-control"
                        placeholder={t("company")}
                      />

                      <Field
                        id="email"
                        type="text"
                        value={values.email}
                        onChange={handleChange}
                        className="form-control"
                        placeholder={t("email")}
                      />
                      <ErrorMessage name="email" component={TextError} />

                      <Field
                        id="message"
                        as="textarea"
                        value={values.message}
                        onChange={handleChange}
                        className="form-control"
                        placeholder={t("message")}
                        rows="3"
                      />
                      <ErrorMessage name="message" component={TextError} />

                      <div className="radio-group">
                        <label>Inquiry Type : </label>
                        <Field name="inquiryType">
                          {
                            ({ field }) => {

                              return inquiryOptions.map(option => {
                                return (

                                  <div className="radio" key={option.key}>
                                    <input
                                      type='radio'
                                      id={option.key}
                                      {...field}
                                      value={option.value}
                                      checked={field.value === option.value}
                                    />
                                    <label htmlFor={option.key}>{option.value}</label>
                                  </div>
                                );
                              })

                            }
                          }
                        </Field>
                      </div>
                      <ErrorMessage name="inquiryType" component={TextError} />

                      <ReCAPTCHA
                        sitekey="6Ld_dAInAAAAAPsDMNQaDgnrSBmPYnrb4KVkmVIy"
                        onChange={onChange}
                        style={{ display: "inline-block" }}
                        ref={captchaRef}

                      />

                      <input
                        type="submit"
                        className="btn btn-primary"
                        value={t("submit")}
                        disabled={isSubmitting}
                      />
                    </div>
                  </div>
                </div>
              </section>
            </Form>

          );
        }}
      </Formik>
    </>
  );
};
export default ContactUsForm;
