import { Formik, Form, ErrorMessage, Field } from "formik";
import * as Yup from "yup";
import TextError from "../../TextError";
import toast from "../../Toast";
import React, { useContext } from "react";
import EmailUpdatesContext from "../../../../context/EmailUpdates/EmailUpdatesContext";
import { contactUs } from '../../../../api'
import { STAY_UP_TO_DATE, STAY_UP_TO_DATE_MSG, EMPLOYMENT_OPPORTUNITY_MSG, BUSINESS_OPPORTUNITY_MSG } from '../../../constants'

const EmailUpdates = ({ m, v, locale }) => {
  const { emailAddress, responseId, setEmailAddress, setStayUptoDate } =
    useContext(EmailUpdatesContext);

  const interestsOptions = m("interestsOptions", { returnObjects: true });;

  const initialValues = {
    mobile: "",
    zipCode: "",
    emailUpdates: true,
    textUpdates: true,
    interests: [],
    advancedNetworkNews: true,
    termsAndConditions: true,
    locale: locale
  };

  const validationSchema = Yup.object({
    mobile: Yup.string().required(v("validations.mobileNumber")).matches(/^\d{10}$/, "Please enter a valid 10 digit number"),
    zipCode: Yup.string().required(v("validations.zipCode")).matches(/^\d{5}$/, v("validations.zipCodeLength")),
    interests: Yup.array().min(1, v("validations.atleastOneOption")).required(),
    termsAndConditions: Yup.bool().oneOf(
      [true],
      v("validations.termsAndConditions")
    ),
  });



  const handleSubmit = async (values, { resetForm }) => {
    let msg = v("STAY_UP_TO_DATE_MSG")
    // if (values.interests.length === 1) {
    //   msg = values.interests[0] === interestsOptions.at(0).value ? BUSINESS_OPPORTUNITY_MSG : EMPLOYMENT_OPPORTUNITY_MSG
    // }
    interestsOptions.map(item => {
      values[item.key] = values.interests.includes(item.value) ? true : false;
    })
    delete values.interests;

    try {

      await contactUs(STAY_UP_TO_DATE, { ...values, responseId, email: emailAddress, })
      toast({
        type: "success",
        message: msg,
      });
      setStayUptoDate(false);
      setEmailAddress("");
      resetForm();
    } catch (error) {
      toast({ type: 'error', message: 'Something went wrong' })
    }
  };

  return (
    <>
      <Formik
        enableReinitialize={true}
        initialValues={initialValues}
        validationSchema={validationSchema}
        onSubmit={handleSubmit}
      >
        {({ handleChange, values, isSubmitting }) => {
          return (
            <Form>
              <section className="form-section form-email-updates">
                <div className="container">
                  <div className="form-text">
                    <h3>{m("wantToStayUpToDate")}</h3>

                    <span>({m("required")})</span>
                  </div>
                  <div className="row">
                    <div className="col col-6">
                      <div className="form-field">
                        <Field
                          id="mobile"
                          type="text"
                          value={values.mobile}
                          onChange={handleChange}
                          className="form-control"
                          placeholder={m("mobileNumber")}
                        />
                        <ErrorMessage
                          name="mobile"
                          component={TextError}
                        />

                        <Field
                          id="zipCode"
                          type="tel"
                          value={values.zipCode}
                          onChange={handleChange}
                          className="form-control"
                          placeholder={m("zipCode")}
                        />
                        <ErrorMessage name="zipCode" component={TextError} />

                        <h4>{m("interests")}</h4>
                        <Field name="interests">
                          {({ field }) => {
                            return interestsOptions.map((option) => {
                              return (
                                <React.Fragment key={option.key}>
                                  <div className="checkbox">
                                    <input
                                      type="checkbox"
                                      className="form-control"
                                      id={option.key}
                                      {...field}
                                      value={option.value}
                                      checked={field.value.includes(
                                        option.value
                                      )}
                                    ></input>
                                    <label htmlFor={option.key}>
                                      {option.value}
                                    </label>
                                  </div>
                                </React.Fragment>
                              );
                            });
                          }}
                        </Field>
                        <br />
                        <ErrorMessage name="interests" component={TextError} />

                        <h4>{m("jfkUpdates")}</h4>
                        <div className="checkbox">
                          <Field
                            type="checkbox"
                            id="emailUpdates"
                            name="emailUpdates"
                          />
                          <label htmlFor="emailUpdates">{m("emailUpdates")}</label>
                        </div>
                        <div className="checkbox">
                          <Field
                            type="checkbox"
                            id="textUpdates"
                            name="textUpdates"
                          />
                          <label htmlFor="textUpdates">{m("text")}</label>
                        </div>
                        <div className="checkbox">
                          <Field
                            type="checkbox"
                            id="advancedNetworkNews"
                            name="advancedNetworkNews"
                          />
                          <label htmlFor="advancedNetworkNews">
                            {m("advanceNetWorkNews")}
                          </label>
                        </div>
                        <div className="checkbox">
                          <Field
                            type="checkbox"
                            id="termsAndConditions"
                            name="termsAndConditions"
                          />

                          <label htmlFor="termsAndConditions">
                            {m("agree")}&nbsp;
                            <a
                              href="https://www.westfield.com/terms-and-conditions"
                              target="_blank"
                              rel="noreferrer"
                              className="link"
                            >
                              {m("termsOfUse")}
                            </a>{" "}
                            {m("and")}{" "}&nbsp;
                            <a
                              href="https://www.urw.com/Privacy-Policy"
                              target="_blank"
                              rel="noreferrer"
                              className="link"
                            >
                              {m("privacyPolicy")}
                            </a>
                            *
                          </label>
                        </div>
                        <br />
                        <ErrorMessage
                          name="termsAndConditions"
                          component={TextError}
                        />
                        <div className="form-buttons">
                          <input
                            type="submit"
                            className="btn btn-primary"
                            value={m("submit")}
                            disabled={isSubmitting}
                          />
                          <input
                            type="button"
                            onClick={() => {
                              setStayUptoDate(false);
                              setEmailAddress("");
                            }}
                            className="btn btn-secondary"
                            value={m("cancel")}
                          />
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </section>
            </Form>
          );
        }}
      </Formik>
    </>
  );
};

export default EmailUpdates;
