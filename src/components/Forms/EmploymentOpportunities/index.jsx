import { Formik, Form, ErrorMessage, Field } from "formik";
import * as Yup from "yup";
import TextError from "../../TextError";
import toast from "../../Toast";
import { contactUs } from "../../../../api";
import { EMPLOYMENT_OPPORTUNITY } from "../../../constants";
import React, { useState } from "react";
import ReCAPTCHA from "react-google-recaptcha";
import { useRef } from "react";
/**
 * NOTE: In case of any addition/deletion in this option list,
 * Please update the file configuration in lambda/constant/index.js
 * Also, update the logic in lambda/csv-exporter/fileExport/index.js
 */
let employmentInterestsOptions = [];

const EmploymentOpportunitiesForm = ({ onSetShowResponse, t, v, locale }) => {
  employmentInterestsOptions = t("employmentInterestsOptions", { returnObjects: true });

  const captchaRef = useRef(null);
  const [captchaValue, setCaptchaValue] = useState(null);
  const [hideForm, setHideForm] = useState(false);


  const onChange = (value) => {
    setCaptchaValue(value);
  };

  const initialValues = {
    firstName: "",
    lastName: "",
    email: "",
    verifyEmail: "",
    mobile: "",
    homeAddress1: "",
    homeAddress2: "",
    city: "",
    state: "NY",
    zipCode: "",
    airportEmploymentExperience: "",
    employmentInterests: [],
    emailUpdates: true,
    textUpdates: false,
    advancedNetworkNews: true,
    termsAndConditions: true,
    locale: locale,
  };

  const validationSchema = Yup.object({
    firstName: Yup.string().required(v("validations.firstName")),
    lastName: Yup.string().required(v("validations.lastName")),
    email: Yup.string().required(v("validations.email")).email(v("validations.invalidEmail")),
    verifyEmail: Yup.string()
      .email(v("validations.invalidEmail"))
      .oneOf([Yup.ref("email"), null], v("validations.emailMatch"))
      .required(v("validations.email")),
    mobile: Yup.string()
      .required(v("validations.mobileNumber"))
      .matches(/^\d{10}$/, "Please enter a valid 10 digit number"),
    homeAddress1: Yup.string().required(v("validations.homeAddress")),
    city: Yup.string().required(v("validations.city")),
    state: Yup.string().required(v("validations.firstName")),
    zipCode: Yup.string().required(v("validations.zipCode")).matches(/^\d{5}$/, v("validations.zipCodeLength")),
    employmentInterests: Yup.array().min(1, v("validations.atleastOneOption")).required(),
    termsAndConditions: Yup.bool().oneOf([true], v("validations.termsAndConditions")),
  });

  const handleSubmit = async (values, { resetForm }) => {
    try {
      if (captchaValue == null) {
        toast({ type: "error", message: "Please check 'I am not robot'" });
        return false;
      } else {
        employmentInterestsOptions.map((item) => {
          values[item.key] = values.employmentInterests.includes(item.value) ? true : false;
        });
        delete values.employmentInterests;
        delete values.verifyEmail;
        await contactUs(EMPLOYMENT_OPPORTUNITY, values);
        onSetShowResponse(true);
        setCaptchaValue(null);
        captchaRef.current.reset();
        resetForm();
      }
    } catch (error) {
      toast({ type: "error", message: "Something went wrong" });
    }
  };

  return (
    <>
      <Formik enableReinitialize={true} initialValues={initialValues} validationSchema={validationSchema} onSubmit={handleSubmit}>
        {({ handleChange, values, isSubmitting }) => {
          return (
            <Form>
              <section className="form-section">
                <div className="container">
                  <div className="form-text">
                    <h3>{t("workAtNewT8")}</h3>
                    <p> {t("signup")} </p>
                    <span>({t("required")})</span>
                    {hideForm && <p>{t("visit")} <a target="_blank" rel="noreferrer" className="link" href="https://www.caonynj.com">www.caonynj.com</a> {t("oremail")} <a className="link" href="mailto:<EMAIL>"><EMAIL></a></p>}
                  </div>
                  {!hideForm && <div className="row">
                    <div className="col col-6">
                      <div className="form-field">
                        <h4>{t("contactInformation")}</h4>
                        <Field id="firstName" type="text" value={values.firstName} onChange={handleChange} className="form-control" placeholder={t("firstName")} />
                        <ErrorMessage name="firstName" component={TextError} />

                        <Field id="lastName" type="text" value={values.lastName} onChange={handleChange} className="form-control" placeholder={t("lastName")} />
                        <ErrorMessage name="lastName" component={TextError} />

                        <Field id="email" type="text" value={values.email} onChange={handleChange} className="form-control" placeholder={t("email")} />
                        <ErrorMessage name="email" component={TextError} />

                        <Field id="verifyEmail" type="text" value={values.verifyEmail} onChange={handleChange} className="form-control" placeholder={t("verifyEmail")} />
                        <ErrorMessage name="verifyEmail" component={TextError} />

                        <Field id="mobile" type="text" value={values.mobile} onChange={handleChange} className="form-control" placeholder={t("mobileNumber")} />
                        <ErrorMessage name="mobile" component={TextError} />
                        <Field id="homeAddress1" type="text" value={values.homeAddress1} onChange={handleChange} className="form-control" placeholder={t("homeAddress")} />
                        <ErrorMessage name="homeAddress1" component={TextError} />
                        <Field id="homeAddress2" type="text" value={values.homeAddress2} onChange={handleChange} className="form-control" placeholder={t("homeAddress2")} />
                        <Field id="city" type="text" value={values.city} onChange={handleChange} className="form-control" placeholder={t("city")} />
                        <ErrorMessage name="city" component={TextError} />

                        <div className="two-colum">
                          <select className="form-control" defaultValue={values.state}>
                            <option value="AL">Alabama</option>
                            <option value="AK">Alaska</option>
                            <option value="AZ">Arizona</option>
                            <option value="AR">Arkansas</option>
                            <option value="CA">California</option>
                            <option value="CO">Colorado</option>
                            <option value="CT">Connecticut</option>
                            <option value="DE">Delaware</option>
                            <option value="DC">District Of Columbia</option>
                            <option value="FL">Florida</option>
                            <option value="GA">Georgia</option>
                            <option value="HI">Hawaii</option>
                            <option value="ID">Idaho</option>
                            <option value="IL">Illinois</option>
                            <option value="IN">Indiana</option>
                            <option value="IA">Iowa</option>
                            <option value="KS">Kansas</option>
                            <option value="KY">Kentucky</option>
                            <option value="LA">Louisiana</option>
                            <option value="ME">Maine</option>
                            <option value="MD">Maryland</option>
                            <option value="MA">Massachusetts</option>
                            <option value="MI">Michigan</option>
                            <option value="MN">Minnesota</option>
                            <option value="MS">Mississippi</option>
                            <option value="MO">Missouri</option>
                            <option value="MT">Montana</option>
                            <option value="NE">Nebraska</option>
                            <option value="NV">Nevada</option>
                            <option value="NH">New Hampshire</option>
                            <option value="NJ">New Jersey</option>
                            <option value="NM">New Mexico</option>
                            <option value="NY">New York</option>
                            <option value="NC">North Carolina</option>
                            <option value="ND">North Dakota</option>
                            <option value="OH">Ohio</option>
                            <option value="OK">Oklahoma</option>
                            <option value="OR">Oregon</option>
                            <option value="PA">Pennsylvania</option>
                            <option value="RI">Rhode Island</option>
                            <option value="SC">South Carolina</option>
                            <option value="SD">South Dakota</option>
                            <option value="TN">Tennessee</option>
                            <option value="TX">Texas</option>
                            <option value="UT">Utah</option>
                            <option value="VT">Vermont</option>
                            <option value="VA">Virginia</option>
                            <option value="WA">Washington</option>
                            <option value="WV">West Virginia</option>
                            <option value="WI">Wisconsin</option>
                            <option value="WY">Wyoming</option>
                          </select>

                          <Field id="zipCode" type="tel" value={values.zipCode} onChange={handleChange} className="form-control" placeholder={t("zipCode")} />
                        </div>
                        <ErrorMessage name="zipCode" component={TextError} />
                      </div>
                    </div>
                    <div className="col col-6">
                      <div className="form-field">
                        <h4>{t("employmentInterests")}</h4>
                        <h5>{t("interestInEmploymentOpportunities")}</h5>
                        <Field name="employmentInterests">
                          {({ field }) => {
                            return employmentInterestsOptions.map((option) => {
                              return (
                                <React.Fragment key={option.key}>
                                  <div className="checkbox">
                                    <input type="checkbox" className="form-control" id={option.key} {...field} value={option.value} checked={field?.value?.includes(option?.value) || false}></input>
                                    <label htmlFor={option.key}>{option.value}</label>
                                  </div>
                                </React.Fragment>
                              );
                            });
                          }}
                        </Field>
                        <br />
                        <ErrorMessage name="employmentInterests" component={TextError} />

                        <h5>{t("airportBusinessExperience")}</h5>
                        <Field
                          id="airportEmploymentExperience"
                          as="textarea"
                          value={values.airportEmploymentExperience}
                          onChange={handleChange}
                          className="form-control"
                          placeholder={t("describe")}
                          rows="3"
                        />
                      </div>

                      <h4>{t("jfkUpdates")}</h4>
                      <div className="checkbox">
                        <Field type="checkbox" id="emailUpdates" name="emailUpdates" />
                        <label htmlFor="emailUpdates">{t("emailUpdates")}</label>
                      </div>
                      {/* <div className="checkbox">
                        <Field
                          type="checkbox"
                          id="textUpdates"
                          name="textUpdates"
                        />
                        <label htmlFor="textUpdates">{t("text")}</label>
                      </div> */}
                      <div className="checkbox">
                        <Field type="checkbox" id="advancedNetworkNews" name="advancedNetworkNews" />
                        <label htmlFor="advancedNetworkNews">{t("advanceNetWorkNews")}</label>
                      </div>
                      <div className="checkbox">
                        <Field type="checkbox" id="termsAndConditions" name="termsAndConditions" />

                        <label htmlFor="termsAndConditions">
                          {t("agree")} &nbsp;
                          <a href="https://www.westfield.com/terms-and-conditions/" target="_blank" rel="noreferrer" className="link">
                            {t("termsOfUse")}
                          </a>{" "}
                          {t("and")} &nbsp;
                          <a href="https://www.urw.com/Privacy-Policy/" target="_blank" rel="noreferrer" className="link">
                            {t("privacyPolicy")}
                          </a>
                        </label>
                      </div>
                      <br />
                      <ErrorMessage name="termsAndConditions" component={TextError} />
                      <ReCAPTCHA sitekey="6Ld_dAInAAAAAPsDMNQaDgnrSBmPYnrb4KVkmVIy" onChange={onChange} style={{ display: "inline-block" }} ref={captchaRef} />
                      <input type="submit" className="btn btn-primary" value={t("submit")} disabled={isSubmitting} />
                    </div>
                  </div>}
                </div>
              </section>
            </Form>
          );
        }}
      </Formik>
    </>
  );
};

export default EmploymentOpportunitiesForm;
