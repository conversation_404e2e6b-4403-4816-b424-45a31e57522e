import { Formik, Form, ErrorMessage, Field } from "formik";
import * as Yup from "yup";
import TextError from "../../TextError";
import toast from "../../Toast";
import { contactUs } from "../../../../api";
import { BUSINESS_OPPORTUNITY } from "../../../constants";
import React, { useState } from "react";
import ReCAPTCHA from "react-google-recaptcha";
import { useRef } from "react";
/**
 * NOTE: In case of any addition/deletion in this option list,
 * Please update the file configuration in lambda/constant/index.js
 * Also, update the logic in lambda/csv-exporter/fileExport/index.js
 */

const BusinessOpportunitiesForm = ({ onSetShowResponse, t, v, locale }) => {
  let businessIntrestsOptions = [];
  let certificationOptions = [];
  businessIntrestsOptions = t("businessInterestsOptions", { returnObjects: true });
  certificationOptions = t("certificationsOptions", { returnObjects: true });

  const captchaRef = useRef(null);
  const [captchaValue, setCaptchaValue] = useState(null);
  const [hideForm, setHideForm] = useState(false);

  const onChange = (value) => {
    setCaptchaValue(value);
  };

  const initialValues = {
    firstName: "",
    lastName: "",
    email: "",
    verifyEmail: "",
    mobile: "",
    companyName: "",
    businessAddress1: "",
    businessAddress2: "",
    city: "",
    state: "NY",
    zipCode: "",
    website: "",
    yearsInBusiness: "",
    businessInterests: [],
    certification: [],
    aboutBusiness: "",
    airportExperience: "",
    emailUpdates: true,
    textUpdates: false,
    advancedNetworkNews: true,
    termsAndConditions: true,
    locale: locale,
  };


  function adjustQuotes(value) {
    // remove single / double quotes from first and last position of the string 
    // and convert all the double quotes to single quotes within a string. 
    return value.replace(/^['"]|['"]$/g, '').replace(/"/g, "'");
  }

  const validationSchema = Yup.object({
    firstName: Yup.string().required(v("validations.firstName")),
    lastName: Yup.string().required(v("validations.lastName")),
    email: Yup.string().email(v("validations.invalidEmail")).required(v("validations.email")),
    verifyEmail: Yup.string()
      .email(v("validations.invalidEmail"))
      .oneOf([Yup.ref("email"), null], v("validations.emailMatch"))
      .required(v("validations.email")),
    mobile: Yup.string()
      .required(v("validations.mobileNumber"))
      .matches(/^\d{10}$/, v("validations.validNumber")),
    companyName: Yup.string().required(v("validations.companyName")),
    businessAddress1: Yup.string().required(v("validations.businessAddress")),
    city: Yup.string().required(v("validations.city")),
    state: Yup.string().required(v("validations.firstName")),
    zipCode: Yup.string().required(v("validations.zipCode")).matches(/^\d{5}$/, v("validations.zipCodeLength")),
    yearsInBusiness: Yup.string().required(v("validations.yearsInBusiness")),
    businessInterests: Yup.array().min(1, v("validations.atleastOneOption")).required(),
    aboutBusiness: Yup.string().required(v("validations.description")),
    airportExperience: Yup.string().required(v("validations.description")),
    termsAndConditions: Yup.bool().oneOf([true], v("validations.termsAndConditions")),
  });

  const handleSubmit = async (values, { resetForm }) => {
    try {
      if (captchaValue == null) {
        toast({ type: "error", message: "Please check 'I am not robot'" });
        return false;
      } else {
        businessIntrestsOptions.map((item) => {
          values[item.key] = values?.businessInterests.includes(item.value) ? true : false;
        });
        delete values.businessInterests;
        delete values.verifyEmail;

        certificationOptions?.map((item) => {
          values[item.key] = values.certification.includes(item.value) ? true : false;
        });
        delete values.certification;

        await contactUs(BUSINESS_OPPORTUNITY, values);
        onSetShowResponse(true);
        setCaptchaValue(null);
        captchaRef.current.reset();
        resetForm();
      }
    } catch (error) {
      toast({ type: "error", message: "Something went wrong" });
    }
  };

  return (
    <>
      <Formik enableReinitialize={true} initialValues={initialValues} validationSchema={validationSchema} onSubmit={handleSubmit}>
        {({ handleChange, values, isSubmitting, setFieldValue }) => {
          return (
            <Form>
              <section className="form-section">
                <div className="container">
                  <div className="form-text">
                    <h3>{t("joinSupplierNetwork")}</h3>
                    <span>({t("required")})</span>
                    {hideForm && (<><p>{t("register")} </p>
                      <br />
                      <p>{t("visitus")} <a target="_blank" rel="noreferrer" className="link" href="https://www.urwairports.com/#diversity">www.urwairports.com</a> {t("signuptoday")}</p></>)}
                  </div>
                  {/* As per client request, hide the form */}
                  {(!hideForm && <div className="row">
                    <div className="col col-6">
                      <div className="form-field">
                        <h4>{t("contactInformation")}</h4>
                        <Field id="firstName" type="text" value={values.firstName} onChange={handleChange} className="form-control" placeholder={t("firstName")} />
                        <ErrorMessage name="firstName" component={TextError} />

                        <Field id="lastName" type="text" value={values.lastName} onChange={handleChange} className="form-control" placeholder={t("lastName")} />
                        <ErrorMessage name="lastName" component={TextError} />

                        <Field id="email" type="text" value={values.email} onChange={handleChange} className="form-control" placeholder={t("email")} />
                        <ErrorMessage name="email" component={TextError} />

                        <Field id="verifyEmail" type="text" value={values.verifyEmail} onChange={handleChange} className="form-control" placeholder={t("verifyEmail")} />
                        <ErrorMessage name="verifyEmail" component={TextError} />

                        <Field id="mobile" type="text" value={values.mobile} onChange={handleChange} className="form-control" placeholder={t("mobileNumber")} />
                        <ErrorMessage name="mobile" component={TextError} />
                      </div>
                      <div className="form-field">
                        <h4>{t("companyInformation")}</h4>
                        <Field id="companyName" type="text" value={values.companyName} onChange={handleChange} className="form-control" placeholder={t("companyName")} />
                        <ErrorMessage name="companyName" component={TextError} />
                        <Field id="businessAddress1" type="text" value={values.businessAddress1} onChange={handleChange} className="form-control" placeholder={t("businessAddress")} />
                        <ErrorMessage name="businessAddress1" component={TextError} />
                        <Field id="businessAddress2" type="text" value={values.businessAddress2} onChange={handleChange} className="form-control" placeholder={t("businessAddress2")} />

                        <Field id="city" type="text" value={values.city} onChange={handleChange} className="form-control" placeholder={t("city")} />
                        <ErrorMessage name="city" component={TextError} />

                        <div className="two-colum">
                          <select className="form-control" defaultValue={values.state}>
                            <option value="AL">Alabama</option>
                            <option value="AK">Alaska</option>
                            <option value="AZ">Arizona</option>
                            <option value="AR">Arkansas</option>
                            <option value="CA">California</option>
                            <option value="CO">Colorado</option>
                            <option value="CT">Connecticut</option>
                            <option value="DE">Delaware</option>
                            <option value="DC">District Of Columbia</option>
                            <option value="FL">Florida</option>
                            <option value="GA">Georgia</option>
                            <option value="HI">Hawaii</option>
                            <option value="ID">Idaho</option>
                            <option value="IL">Illinois</option>
                            <option value="IN">Indiana</option>
                            <option value="IA">Iowa</option>
                            <option value="KS">Kansas</option>
                            <option value="KY">Kentucky</option>
                            <option value="LA">Louisiana</option>
                            <option value="ME">Maine</option>
                            <option value="MD">Maryland</option>
                            <option value="MA">Massachusetts</option>
                            <option value="MI">Michigan</option>
                            <option value="MN">Minnesota</option>
                            <option value="MS">Mississippi</option>
                            <option value="MO">Missouri</option>
                            <option value="MT">Montana</option>
                            <option value="NE">Nebraska</option>
                            <option value="NV">Nevada</option>
                            <option value="NH">New Hampshire</option>
                            <option value="NJ">New Jersey</option>
                            <option value="NM">New Mexico</option>
                            <option value="NY">New York</option>
                            <option value="NC">North Carolina</option>
                            <option value="ND">North Dakota</option>
                            <option value="OH">Ohio</option>
                            <option value="OK">Oklahoma</option>
                            <option value="OR">Oregon</option>
                            <option value="PA">Pennsylvania</option>
                            <option value="RI">Rhode Island</option>
                            <option value="SC">South Carolina</option>
                            <option value="SD">South Dakota</option>
                            <option value="TN">Tennessee</option>
                            <option value="TX">Texas</option>
                            <option value="UT">Utah</option>
                            <option value="VT">Vermont</option>
                            <option value="VA">Virginia</option>
                            <option value="WA">Washington</option>
                            <option value="WV">West Virginia</option>
                            <option value="WI">Wisconsin</option>
                            <option value="WY">Wyoming</option>
                          </select>

                          <Field id="zipCode" type="tel" value={values.zipCode} onChange={handleChange} className="form-control" placeholder={t("zipCode")} />
                        </div>
                        <ErrorMessage name="zipCode" component={TextError} />
                        <Field id="website" type="text" value={values.website} onChange={handleChange} className="form-control" placeholder={t("website")} />
                      </div>
                    </div>
                    <div className="col col-6">
                      <div className="form-field">
                        <h4>{t("businessInterests")}</h4>
                        <h5>{t("interestInBusinessOpportunities")}</h5>
                        <Field name="businessInterests">
                          {({ field }) => {
                            return businessIntrestsOptions.map((option) => {
                              return (
                                <React.Fragment key={option.key}>
                                  <div className="checkbox">
                                    <input type="checkbox" className="form-control" id={option.key} {...field} value={option.value} checked={field?.value?.includes(option?.value) || false}></input>
                                    <label htmlFor={option.key}>{option.value}</label>
                                  </div>
                                </React.Fragment>
                              );
                            });
                          }}
                        </Field>
                        <br />
                        <ErrorMessage name="businessInterests" component={TextError} />
                      </div>
                      <div className="form-field">
                        <h4>{t("certifications")}</h4>
                        <h5>{t("isBusinessCertified")}</h5>
                        <Field name="certification">
                          {({ field }) => {
                            return certificationOptions.map((option) => {
                              return (
                                <React.Fragment key={option.key}>
                                  <div className="checkbox">
                                    <input type="checkbox" className="form-control" id={option.key} {...field} value={option.value} checked={field?.value?.includes(option?.value) || false}></input>
                                    <label htmlFor={option.key}>{option.value}</label>
                                  </div>
                                </React.Fragment>
                              );
                            });
                          }}
                        </Field>
                      </div>
                      <div className="form-field">
                        <h4>{t("about")}</h4>
                        <Field
                          id="aboutBusiness"
                          as="textarea"
                          value={values.aboutBusiness}
                          onBlur={(e) => {
                            setFieldValue("aboutBusiness", adjustQuotes(e.target.value));
                          }}
                          onChange={handleChange}
                          className="form-control"
                          placeholder={t("describeBusiness")} rows="3" />
                        <ErrorMessage name="aboutBusiness" component={TextError} />
                        <Field
                          id="airportExperience"
                          as="textarea"
                          value={values.airportExperience}
                          onBlur={(e) => {
                            setFieldValue("airportExperience", adjustQuotes(e.target.value));
                          }}
                          onChange={handleChange}
                          className="form-control"
                          placeholder={t("airportBusinessExperience")}
                          rows="4"
                        />
                        <ErrorMessage name="airportExperience" component={TextError} />

                        <Field id="yearsInBusiness" type="text" value={values.yearsInBusiness} onChange={handleChange} className="form-control" placeholder={t("yearsInBusiness")} />
                        <ErrorMessage name="yearsInBusiness" component={TextError} />
                      </div>
                      <h4>{t("jfkUpdates")}</h4>
                      <div className="checkbox">
                        <Field type="checkbox" id="emailUpdates" name="emailUpdates" />
                        <label htmlFor="emailUpdates">{t("emailUpdates")}</label>
                      </div>
                      {/* <div className="checkbox">
                        <Field
                          type="checkbox"
                          id="textUpdates"
                          name="textUpdates"
                        />
                        <label htmlFor="textUpdates">{t("text")}</label>
                      </div> */}
                      <div className="checkbox">
                        <Field type="checkbox" id="advancedNetworkNews" name="advancedNetworkNews" />
                        <label htmlFor="advancedNetworkNews">{t("advanceNetWorkNews")}</label>
                      </div>
                      <div className="checkbox">
                        <Field type="checkbox" id="termsAndConditions" name="termsAndConditions" />

                        <label htmlFor="termsAndConditions">
                          {t("agree")} &nbsp;
                          <a href="https://www.westfield.com/terms-and-conditions" target="_blank" rel="noreferrer" className="link">
                            {t("termsOfUse")}
                          </a>{" "}
                          {t("and")} &nbsp;
                          <a href="https://www.urw.com/Privacy-Policy" target="_blank" rel="noreferrer" className="link">
                            {t("privacyPolicy")}
                          </a>
                        </label>
                      </div>
                      <br />
                      <ErrorMessage name="termsAndConditions" component={TextError} />
                      <ReCAPTCHA sitekey="6Ld_dAInAAAAAPsDMNQaDgnrSBmPYnrb4KVkmVIy" onChange={onChange} style={{ display: "inline-block" }} ref={captchaRef} />
                      <input type="submit" className="btn btn-secondary" value={t("submit")} disabled={isSubmitting} />
                    </div>
                  </div>)}
                </div>
              </section>
            </Form>
          );
        }}
      </Formik >
    </>
  );
};

export default BusinessOpportunitiesForm;
