import { Formik, Form, ErrorMessage, Field } from "formik";
import * as Yup from "yup";
import TextError from "../../TextError";
import toast from "../../Toast";
import { contactUs } from "../../../../api";
import { MY_T8 } from "../../../constants";
import LinkComponent from "../../../../internationalization/Link";
import styles from './MyT8.module.scss'
import React, { useState } from "react";
import ReCAPTCHA from "react-google-recaptcha"
import { useRef } from "react";
/**
 * NOTE: In case of any addition/deletion in this option list,
 * Please update the file configuration in lambda/constant/index.js
 * Also, update the logic in lambda/csv-exporter/fileExport/index.js
 */
const MyT8Form = ({ t, v, locale }) => {

  const captchaRef = useRef(null);
  const [captchaValue, setCaptchaValue] = useState(null);

  const onChange = (value) => {
    setCaptchaValue(value);
  }

  const initialValues = {
    firstName: "",
    lastName: "",
    email: "",
    verifyEmail: "",
    mobile: "",
    zipCode: "",
    airportBadgeHolder: "",
    whatTerminalsDoYouWorkIn: "",
    currentRole: "",
    interestedInOpportunities: "",
    emailUpdates: true,
    textUpdates: true,
    advancedNetworkNews: true,
    termsAndConditions: true,
    locale: locale,
  };

  const validationSchema = Yup.object({
    firstName: Yup.string().required(v("validations.firstName")),
    lastName: Yup.string().required(v("validations.lastName")),
    email: Yup.string().required(v("validations.email")).email(v("validations.invalidEmail")),
    verifyEmail: Yup.string()
      .email(v("validations.invalidEmail"))
      .oneOf([Yup.ref('email'), null], v("validations.emailMatch"))
      .required(v("validations.email")),
    mobile: Yup.string()
      .required(v("validations.mobileNumber"))
      .matches(/^\d{10}$/, "Please enter a valid 10 digit number"),
    zipCode: Yup.string().required(v("validations.zipCode")).matches(/^\d{5}$/, v("validations.zipCodeLength")),
    airportBadgeHolder: Yup.string().required(
      v("validations.atleastOneOption")
    ),
    interestedInOpportunities: Yup.string().required(
      v("validations.atleastOneOption")
    ),
    termsAndConditions: Yup.bool().oneOf(
      [true],
      v("validations.termsAndConditions")
    ),
  });

  const handleSubmit = async (values, { resetForm }) => {

    try {
      delete values.verifyEmail;

      await contactUs(MY_T8, values);
      toast({ type: "success", message: v("STAY_UP_TO_DATE_MSG") });
      setCaptchaValue(null);
      captchaRef.current.reset();
      resetForm();
    } catch (error) {
      toast({ type: "error", message: "Something went wrong" });
    }
  };




  return (
    <>
      <Formik
        enableReinitialize={true}
        initialValues={initialValues}
        validationSchema={validationSchema}
        onSubmit={handleSubmit}
      >
        {({ handleChange, values, isSubmitting, errors, touched }) => {
          return (
            <Form>
              <section className="form-section">
                <div className="container">
                  <div className="form-text">
                    <h3>
                      {t("employedAtT8")} {t("signupToday")}
                    </h3>
                    <p>{t("signup")}.</p>
                    <span>({t("required")})</span>
                  </div>
                  <div className="row">
                    <div className="col col-6">
                      <div className="form-field">
                        <h4>{t("contactInformation")}</h4>
                        <Field
                          id="firstName"
                          type="text"
                          value={values.firstName}
                          onChange={handleChange}
                          className="form-control"
                          placeholder={t("firstName")}
                        />
                        <ErrorMessage name="firstName" component={TextError} />

                        <Field
                          id="lastName"
                          type="text"
                          value={values.lastName}
                          onChange={handleChange}
                          className="form-control"
                          placeholder={t("lastName")}
                        />
                        <ErrorMessage name="lastName" component={TextError} />

                        <Field
                          id="email"
                          type="text"
                          value={values.email}
                          onChange={handleChange}
                          className="form-control"
                          placeholder={t("email")}
                        />
                        <ErrorMessage name="email" component={TextError} />

                        <Field
                          id="verifyEmail"
                          type="text"
                          value={values.verifyEmail}
                          onChange={handleChange}
                          className="form-control"
                          placeholder={t("verifyEmail")}
                        />
                        <ErrorMessage name="verifyEmail" component={TextError} />

                        <Field
                          id="mobile"
                          type="text"
                          value={values.mobile}
                          onChange={handleChange}
                          className="form-control"
                          placeholder={t("mobileNumber")}
                        />
                        <ErrorMessage
                          name="mobile"
                          component={TextError}
                        />

                        <Field
                          id="zipCode"
                          type="tel"
                          value={values.zipCode}
                          onChange={handleChange}
                          className="form-control"
                          placeholder={t("zipCode")}
                        />

                        <ErrorMessage name="zipCode" component={TextError} />
                      </div>
                    </div>
                    <div className="col col-6">
                      <div className="form-field">
                        <h4>{t("interests")}</h4>
                        <h5>{t("airportBadgeHolder")}</h5>
                        <div
                          role="group"
                          aria-labelledby="my-radio-group"
                          style={{ marginBottom: "20px", height: "auto" }}
                        >
                          <label>
                            <Field
                              type="radio"
                              name="airportBadgeHolder"
                              value="Yes"
                            />
                            &nbsp;Yes&nbsp;&nbsp;
                          </label>
                          <label>
                            <Field
                              type="radio"
                              name="airportBadgeHolder"
                              value="No"
                            />
                            &nbsp;No&nbsp;&nbsp;
                          </label>
                        </div>
                        <ErrorMessage
                          name="airportBadgeHolder"
                          component={TextError}
                        />

                        <h5>{t("interestInOpportunities")}</h5>
                        <div
                          role="group"
                          aria-labelledby="my-radio-group"
                          style={{ marginBottom: "20px", height: "auto" }}
                        >
                          <label>
                            <Field
                              type="radio"
                              name="interestedInOpportunities"
                              value="Part Time"
                            />
                            &nbsp;Part Time&nbsp;&nbsp;
                          </label>
                          <label>
                            <Field
                              type="radio"
                              name="interestedInOpportunities"
                              value="Full Time"
                            />
                            &nbsp;Full Time&nbsp;&nbsp;
                          </label>
                          <label>
                            <Field
                              type="radio"
                              name="interestedInOpportunities"
                              value="Both"
                            />
                            &nbsp;Both&nbsp;&nbsp;
                          </label>
                          <label>
                            <Field
                              type="radio"
                              name="interestedInOpportunities"
                              value="Not currently looking"
                            />
                            &nbsp;Not currently looking&nbsp;&nbsp;
                          </label>
                        </div>
                        <ErrorMessage
                          name="interestedInOpportunities"
                          component={TextError}
                        />

                        <h5>{t("whatTerminal")} </h5>
                        <Field
                          id="whatTerminalsDoYouWorkIn"
                          as="textarea"
                          value={values.whatTerminalsDoYouWorkIn}
                          onChange={handleChange}
                          className="form-control"
                          placeholder={t("describe")}
                          rows="3"
                        />

                        <h5>{t("currentRole")}</h5>
                        <Field
                          id="currentRole"
                          as="textarea"
                          value={values.currentRole}
                          onChange={handleChange}
                          className="form-control"
                          placeholder={t("describe")}
                          rows="3"
                        />
                      </div>

                      <h4>{t("jfkUpdates")}</h4>
                      <div className="checkbox">
                        <Field
                          type="checkbox"
                          id="emailUpdates"
                          name="emailUpdates"
                        />
                        <label htmlFor="emailUpdates">
                          {t("emailUpdates")}
                        </label>
                      </div>
                      <div className="checkbox">
                        <Field
                          type="checkbox"
                          id="textUpdates"
                          name="textUpdates"
                        />
                        <label htmlFor="textUpdates">{t("text")}</label>
                      </div>
                      <div className="checkbox">
                        <Field
                          type="checkbox"
                          id="advancedNetworkNews"
                          name="advancedNetworkNews"
                        />
                        <label htmlFor="advancedNetworkNews">
                          {t("advanceNetWorkNews")}
                        </label>
                      </div>
                      <div className="checkbox">
                        <Field
                          type="checkbox"
                          id="termsAndConditions"
                          name="termsAndConditions"
                        />

                        <label htmlFor="termsAndConditions">
                          {t("agree")}{" "}&nbsp;
                          <a
                            href="https://www.westfield.com/terms-and-conditions/"
                            target="_blank"
                            rel="noreferrer"
                            className="link"
                          >
                            {t("termsOfUse")}
                          </a>{" "}
                          {t("and")}{" "}&nbsp;
                          <a
                            href="https://www.urw.com/Privacy-Policy/"
                            target="_blank"
                            rel="noreferrer"
                            className="link"
                          >
                            {t("privacyPolicy")}
                          </a>
                        </label>
                      </div>
                      <br />
                      <ErrorMessage
                        name="termsAndConditions"
                        component={TextError}
                      />
                      <ReCAPTCHA
                        sitekey="6Ld_dAInAAAAAPsDMNQaDgnrSBmPYnrb4KVkmVIy"
                        onChange={onChange}
                        style={{ display: "inline-block" }}
                        ref={captchaRef}
                      />
                      <input
                        type="submit"
                        className="btn btn-primary"
                        value={t("submit")}
                        disabled={isSubmitting}
                      />
                      <LinkComponent
                        href="/myt8"
                        linktext={t("cancel")}
                        style={{ left: "180px" }}
                        className={`${styles["btn-myt8"]} btn btn-secondary`}
                      ></LinkComponent>
                      {/* <input
                        type="button"
                        className="btn btn-secondary"
                        value={t("cancel")}
                        style={{ left: "180px" }}
                        // onClick={() => toggleMyT8Form(false)}
                      /> */}
                    </div>
                  </div>
                </div>
              </section>
            </Form>
          );
        }}
      </Formik>
    </>
  );
};

export default MyT8Form;
