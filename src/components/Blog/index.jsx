import { documentToReactComponents } from "@contentful/rich-text-react-renderer";
import { INLINES } from '@contentful/rich-text-types'
import HeadMeta from "../HeadMeta";
import Slider from "react-slick";
import SocialMediaIcons from "../SocialMediaIcons";
import styles from './Blog.module.scss'

const sliderSettings = {
  prevArrow: false,
  nextArrow: false,
  dots: true,
  infinite: true,
  autoplay: true,
  autoplaySpeed: 3000,
  slidesToShow: 1,
};
var dateOptions = { year: "numeric", month: "long", day: "numeric" };

const options = {
  renderNode: {
    [INLINES.HYPERLINK]: (node) => {
      if ((node.data.uri).includes("youtube.com/embed")) {
        return <span className={styles.IframeVideoClass}><iframe title="Youtube Video" src={node.data.uri} allow="accelerometer; encrypted-media; gyroscope; picture-in-picture" frameBorder="0" allowFullScreen></iframe></span>
      } else {
        return <a href={node?.data?.uri} className="link" target="_blank" rel="noreferrer">
          {node?.content[0]?.value}
        </a>
      }
    }
  }
}

// const renderOptions = {
//   renderNode: {
//     [INLINES.HYPERLINK]: (node) => {
//       return <a href={node.data.uri} target={`${node.data.uri.startsWith("https://") ? '_blank' : ''}`}>{node.content[0].value}</a>;
//     }
//   }
// }

const Blog = ({ blog, previous, next, locale }) => {
  const meta = {
    title: blog?.metaTitle,
    description: blog?.metaDescription,
    keywords: blog?.metaKeywords || "",
    robots: blog?.metaRobots,
    canonicalUrl: blog?.metaCanonicalUrl,

  }

  const primaryTag = blog?.tags || "";
  const secondaryTags = blog?.secondaryTags ? blog?.secondaryTags.join(",") : "";

  let allTags = primaryTag + "," + secondaryTags;
  allTags = allTags
    .split(",")
    .filter(function (item, pos, self) {
      return item != '' && self.indexOf(item) == pos;
    })
    .join(", ");

  return (
    <>
      <HeadMeta meta={meta} />

      <section className="blog-carousel">
        {/* <img src={blog?.bannerImageCollection?.items[0].url} width="" alt="" />
        <div className="container">
          <h1>{blog.title}</h1>
        </div> */}
        <Slider {...sliderSettings} className="carousel">
          {blog?.bannerImageCollection?.items?.map((item, index) => {
            return (
              item && <div className="item" key={index}>
                <img src={item?.url} alt={item?.description} />
              </div>
            );
          })}
        </Slider>
        <div className="category-date">
          <div className="container flex">
            <a href="#">{allTags}</a>
            {/* <span>
              {new Date(blog?.sys?.firstPublishedAt).toLocaleDateString(
                "en-US",
                dateOptions
              )}
            </span> */}
          </div>
        </div>
      </section>

      <section className="blog-section">
        <div className="container">
          <div className="blog-details">
            <h1>{blog?.title}</h1>
            {documentToReactComponents(blog?.description?.json, options)}
          </div>
        </div>
      </section>

      <section className="blog-content">
        <div className="container">
          {/* <SocialMediaIcons /> */}

          <div className="blog-buttons flex noprint">
            <div className="prev">
              <a href={previous ? "/" + locale + `/blog/${previous.slug}` : "#"}>
                <img src="/assets/images/icon-arrow.svg" alt="icon-arrow" />
                <span>PREV</span>
              </a>
            </div>
            <div className="next">
              <a href={next ? "/" + locale + `/blog/${next.slug}` : "#"}>
                <span>NEXT</span>
                <img src="/assets/images/icon-arrow.svg" alt="icon-arrow" />
              </a>
            </div>
          </div>
        </div>
      </section>
    </>
  );
};

export default Blog;
