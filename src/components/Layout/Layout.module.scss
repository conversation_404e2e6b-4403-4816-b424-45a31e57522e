.wrapper {
	width: 100%;
}



.scrollTop {
	position: fixed;
	right: 30px;
	bottom: 42px;
	opacity: 0;
	transition: all 0.4s ease-in-out 0s;

	a {
		background-color: #f5f5f5;
		width: 40px;
		height: 40px;
		transition: all 0.4s ease-in-out 0s;
		border-radius: 50px;
		position: relative;
		display: flex;
		align-items: center;
		justify-content: center;
		box-shadow: 0px 7px 7px 0px rgba(0, 0, 0, 0.20);

		&::before {
			content: "";
			background: url(/assets/images/menu-arrow.svg) no-repeat center center;
			transform: rotate(180deg);
			width: 20px;
			height: 20px;
			display: block;
		}

		&:hover {
			background-color: #ffffff;

		}
	}

	@media screen and (max-width: 767px) {
		right: 20px;
		bottom: 90px;
	}
}