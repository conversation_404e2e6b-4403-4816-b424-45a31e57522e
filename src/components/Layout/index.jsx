import Header from "../Header";
import Footer from "../Footer";
import Carousel from "../Carousel";
import styles from "./Layout.module.scss";

const Layout = (props) => {
  const {isHome,heroImages,topPartnerLogos,footerPartnerLogos,footerCommunityPartnerLogos, navLinksText, footerLinksText} = props;
  const handleClick = () => {
    window.scrollTo({ top: 0, behavior: "smooth" });
  };

  if (typeof window !== "undefined") {
    window.addEventListener("scroll", function () {
      var pos = window.scrollY;
      if (pos > 300) {
        document.getElementById('stop').style.opacity = '1';    
      } else {
        document.getElementById('stop').style.opacity = '0';
      }
    });
  }


  return (
    <div className={styles.wrapper}>
      <Header topPartnerLogos={topPartnerLogos} navLinksText={navLinksText} />
      {isHome && <Carousel heroImages={heroImages} />}
      <main className="content">{props.children}</main>
      <Footer footerPartnerLogos={footerPartnerLogos} footerCommunityPartnerLogos={footerCommunityPartnerLogos} footerLinksText={footerLinksText}/>
      <div
        className={`${styles.scrollTop} ${'noprint'}`}
        id="stop"
        onClick={handleClick}
      >
        <a href="#!"></a>
      </div>
    </div>
  );
};

export default Layout;
