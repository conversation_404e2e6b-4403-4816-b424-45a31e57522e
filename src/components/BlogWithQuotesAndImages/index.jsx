import HeadMeta from "../HeadMeta";
import { documentToReactComponents } from "@contentful/rich-text-react-renderer";
import Slider from "react-slick";
import SocialMediaIcons from "../SocialMediaIcons";
import { INLINES } from '@contentful/rich-text-types'
import styles from './BlogWithQuotesAndImages.module.scss'

const BlogWithQuotesAndImages = ({ blog, previous, next, locale }) => {
  const meta = {
    title: blog?.metaTitle,
    description: blog?.metaDescription,
    keywords: blog?.metaKeywords || "",
    robots: blog?.metaRobots,
    canonicalUrl: blog?.metaCanonicalUrl,
  };
  var dateOptions = { year: "numeric", month: "long", day: "numeric" };
  const sliderSettings = {
    prevArrow: false,
    nextArrow: false,
    dots: true,
    infinite: true,
    autoplay: true,
    autoplaySpeed: 3000,
    slidesToShow: 1,
  };

  const options = {
    renderNode: {
      [INLINES.HYPERLINK]: (node) => {
        if ((node.data.uri).includes("youtube.com/embed")) {
          return <span className={styles.IframeVideoClass}><iframe title="Youtube Video" src={node.data.uri} allow="accelerometer; encrypted-media; gyroscope; picture-in-picture" frameBorder="0" allowFullScreen></iframe></span>
        } else {
          return <a href={node?.data?.uri} className="link" target="_blank" rel="noreferrer">
            {node?.content[0]?.value}
          </a>
        }
      }
    }
  }

  const primaryTag = blog?.tags || "";
  const secondaryTags = blog?.secondaryTags
    ? blog?.secondaryTags.join(",")
    : "";

  let allTags = primaryTag + "," + secondaryTags;
  allTags = allTags
    .split(",")
    .filter(function (item, pos, self) {
      return item != "" && self.indexOf(item) == pos;
    })
    .join(", ");
  return (
    <>
      <HeadMeta meta={meta} />

      <section className="blog-carousel blog-carousel-img">
        <Slider {...sliderSettings} className="carousel">
          {blog?.bannerImageCollection?.items?.map((item, index) => {
            return (
              item && (
                <div className="item" key={index}>
                  <img src={item?.url} alt={item?.description} />
                </div>
              )
            );
          })}
        </Slider>{" "}
        <div className="category-date">
          <div className="container flex">
            <a href="#">{allTags}</a>
            {/* <span>
              {new Date(blog?.sys?.firstPublishedAt).toLocaleDateString(
                "en-US",
                dateOptions
              )}
            </span> */}
          </div>
        </div>
      </section>

      <section className="blog-content">
        <div className="container">
          <h2>{blog?.title}</h2>

          <div className="blog-col row">
            {blog?.paragraph2 ? (
              <>
                <div className="col col-6">
                  {documentToReactComponents(blog?.paragraph1?.json, options)}
                </div>
                <div className="col col-6">
                  {documentToReactComponents(blog?.paragraph2?.json, options)}
                </div>
              </>
            ) : (
              <>
                <div className="col col-12">
                  {documentToReactComponents(blog?.paragraph1?.json, options)}
                </div>
              </>
            )}
          </div>

          {blog?.quote1 && (
            <div className="blog-quotes flex">
              <div className="quotes-icon">
                <img src="/assets/images/icon-quotes.jpg" alt="icon-quotes" />
              </div>
              <h4>{blog?.quote1}</h4>
            </div>
          )}

          {blog?.image1?.url && (
            <div className="blog-img">
              <img src={blog?.image1.url} alt={blog?.image1?.description} />
            </div>
          )}

          {blog?.blogVideo?.url && (
            <div className="blog-video">
              <video autoPlay muted src={blog?.blogVideo?.url}></video>
            </div>
          )}

          {blog?.subTitle && <h3>{blog?.subTitle}</h3>}

          <div className="blog-col row">
            {blog?.paragraph4 ? (
              <>
                <div className="col col-6">
                  {documentToReactComponents(blog?.paragraph3?.json, options)}
                </div>
                <div className="col col-6">
                  {documentToReactComponents(blog?.paragraph4?.json, options)}
                </div>
              </>
            ) : (
              <>
                <div className="col col-12">
                  {documentToReactComponents(blog?.paragraph3?.json, options)}
                </div>
              </>
            )}
          </div>

          <div className="blog-col-quotes row flex">
            <div className="col col-6">
              <h4>{blog?.quote2}</h4>
            </div>
            <div className="col col-6">
              <img src={blog?.image2?.url} alt={blog?.image2?.description} />
            </div>
          </div>

          {/* <SocialMediaIcons /> */}

          <div className="blog-buttons flex noprint">
            <div className="prev">
              <a
                href={previous ? "/" + locale + `/blog/${previous.slug}` : "#"}
              >
                <img src="/assets/images/icon-arrow.svg" alt="icon-arrow" />
                <span>PREV</span>
              </a>
            </div>
            <div className="next">
              <a href={next ? "/" + locale + `/blog/${next.slug}` : "#"}>
                <span>NEXT</span>
                <img src="/assets/images/icon-arrow.svg" alt="icon-arrow" />
              </a>
            </div>
          </div>
        </div>
      </section>
    </>
  );
};

export default BlogWithQuotesAndImages;
