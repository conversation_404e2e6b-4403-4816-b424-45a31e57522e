import { useContext, useState } from "react";
import EmailUpdatesContext from "../../../context/EmailUpdates/EmailUpdatesContext";
import toast from "../Toast";
import { contactUs } from '../../../api'
import { EMAIL_ONLY } from "../../constants";
const EmailUpdates = ({stayUptoDateText}) => {

  const { emailAddress, setEmailAddress, setResponseId, setStayUptoDate } =
    useContext(EmailUpdatesContext);

  const [isSubmitting, setIsSubmitting] = useState(false)

  const handleChange = (email) => {
    setEmailAddress(email);
  };

  const handleClick = async (e) => {
    e.preventDefault();

    if (!/^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,4}$/i.test(emailAddress)) {
      toast({
        type: "error",
        message: stayUptoDateText.validEmail,
      });
      return false;
    }
    try {
      setIsSubmitting(true)
      const res = await contactUs('email', { email: emailAddress });
      setResponseId(res.responseId)
      setStayUptoDate(true);
    } catch (error) {
      toast({ type: 'error', message: stayUptoDateText.errorMessage })
      setIsSubmitting(false)
    }
    setIsSubmitting(false)
  };

  return (
    <>
      <section className="subscribe-section">
        <div className="container">
          <form action="">
            <div className="form flex">
              <label>{stayUptoDateText.stayUptoDate}</label>
              <input
                type="text"
                placeholder={stayUptoDateText.enterEmail}
                className="form-control"
                value={emailAddress}
                onChange={(e) => handleChange(e.target.value)}
              />
              <input
                type="submit"
                value={stayUptoDateText.submit}
                disabled={isSubmitting}
                className="btn btn-secondary"
                onClick={handleClick}
              />
            </div>
          </form>
        </div>
      </section>
    </>
  );
};

export default EmailUpdates;
