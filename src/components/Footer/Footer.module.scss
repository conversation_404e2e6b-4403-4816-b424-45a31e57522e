.footer {
  background: var(--gray) url(/assets/images/shadow-bottom.png) no-repeat center top;

  img {
    filter: grayscale(1);
    opacity: 0.4;
    transition: all 0.2s ease-in-out;

    &:hover {
      filter: grayscale(0);
      opacity: 1;
    }
  }
}

.partners-top {
  padding: 65px 0 50px;
}

.partners-main {
  min-width: 160px;
  padding: 15px 40px 15px 0;
  margin-right: 40px;
  border-right: solid 1px var(--gray-dr);
}

.partners-sub {
  width: 100%;
}

.partners-title {
  position: relative;
  text-align: center;

  span {
    position: relative;
    display: inline-block;
    padding: 5px 30px;
    background-color: var(--gray);
    color: var(--secondary);
  }

  &::before {
    content: '';
    position: absolute;
    left: 0;
    top: 50%;
    height: 1px;
    width: 100%;
    background-color: var(--gray-dr);
  }
}

.partners-logo {
  padding: 40px 0 50px 0;
}

.footer-bottom {
  background: var(--primary) url(/assets/images/shadow-bottom.png) no-repeat center top;
  color: white;
  font-size: 12px;
  padding: 22px 0;

  a {
    color: white;
  }
}

.copyright {
  margin-right: auto;
}

.flex {
  display: flex;
  align-items: center;
}

.footer-link {
  margin: 0 auto;
  a {
    margin: 0 5px;
  }
}

@media (max-width: 991px) {
  .partners-main {
    padding: 10px 10px 20px 10px;
  }
}

@media screen and (max-width: 767px) {
  .footer {
    display: flex;
    background-size: 130%;
  }

  .footer-icon img {
    width: 38px;
  }

  .footer {
    flex-direction: column;
  }

  .footer-bottom {
    background-size: 130%;
    .flex {
      justify-content: center;
    }

    .copyright {
      margin: 0;
    }
  }

  .partners-top {
    padding: 40px 0 30px 0;
    flex-direction: column;

    .partners-sub {
      flex-wrap: wrap;
      justify-content: center;
      max-width: 300px;
      span {
        max-width: 140px;
        min-width: 140px;
        text-align: center;
        padding: 5px 12px;
        &:nth-child(3) img {
          max-width: 30px;
        }
        &:last-child img {
          max-width: 70px;
        }
      }
    }

    .partners-main {
      border-right: none;
      text-align: center;
      padding: 0;
      max-width: 90px;
      min-width: auto;
      margin: 0 auto 20px;
    }
  }

  .partners-logo {
    flex-wrap: wrap;
    justify-content: center;
    padding: 20px 0;

    span {
      padding: 5px 10px;
      max-width: 110px;
    }
  }

  .flex {
    flex-wrap: wrap;
  }

  .footer-link {
    margin: 5px 0;
  }
}
