import React from "react";
import styles from "./Footer.module.scss";
const Footer = ({ footerPartner<PERSON>ogos, footerCommunityPartnerLogos, footerLinksText }) => {
 
  return (
    <>
      <footer className={`${styles.footer} ${'noprint'}`}>
        <div className={styles["footer-top"]}>
          <div className="container">
            <div className={`${styles["partners-top"]} flex`}>
              <div className={styles["partners-main"]}>
                <img src="/assets/images/JFKT8-IP.png" width="180" alt="logo-JFKT8" />
              </div>
              <div className={`${styles["partners-sub"]} flex-space-between`}>
                {footerPartnerLogos?.items[0]?.partnersCollection?.items?.map(
                  (item, key) => {
                    return (
                      item && <React.Fragment key={key}>
                        <span>
                          <a href={item?.url} target="_blank" rel="noreferrer">
                            <img
                              src={item?.logo?.url}
                              width={item?.logoWidth}
                              alt={item?.logo?.description}
                            />
                          </a>
                        </span>
                      </React.Fragment>
                    );
                  }
                )}
              </div>
            </div>
            <div className={styles["partners-bottom"]}>
              <div className={styles["partners-title"]}>
                <span>{footerLinksText.communityPartners}</span>
              </div>
              <div className={`${styles["partners-logo"]} flex-space-between`}>
                
                {footerCommunityPartnerLogos?.items[0]?.communityPartnersCollection?.items?.map(
                  (item, key) => {

                    return (
                      item && <React.Fragment key={key}>
                        <span>
                          <a href={item?.url} target="_blank" rel="noreferrer">
                            <img
                              src={item?.logo?.url}
                              width={item?.logoWidth}
                              alt={item?.logo?.description}
                            />
                          </a>
                        </span>
                      </React.Fragment>
                    );
                  }
                )}
              </div>
            </div>
          </div>
        </div>
        <div className={styles["footer-bottom"]}>
          <div className={`container ${styles.flex}`}>
            <span className={styles.copyright}>
              {footerLinksText.copyright}
            </span>
            <div className={styles["footer-link"]}>
              <a
                href="https://www.urw.com/Privacy-Policy"
                target="_blank"
                rel="noreferrer"
                className="link"
              >
                {footerLinksText.privacyPolicy}
              </a>
              <a
                href="https://www.westfield.com/terms-and-conditions"
                target="_blank"
                rel="noreferrer"
                className="link"
              >
                {footerLinksText.termsOfUse}
              </a>
            </div>
            <span>
              {footerLinksText.commercialDevelopment}{" "}
              <a href="https://www.urwairports.com" target="_blank"
                rel="noreferrer" className="link">
                {footerLinksText.urwAirports}
              </a>
            </span>
          </div>
        </div>
      </footer>
    </>
  );
};

export default Footer;
