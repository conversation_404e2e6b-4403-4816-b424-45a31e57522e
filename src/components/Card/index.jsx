import LinkWithImage from "../../../internationalization/LinkWithImage";
const isUrlAbsolute = (url) =>
  url.indexOf("//") === 0
    ? true
    : url.indexOf("://") === -1
    ? false
    : url.indexOf(".") === -1
    ? false
    : url.indexOf("/") === -1
    ? false
    : url.indexOf(":") > url.indexOf("/")
    ? false
    : url.indexOf("://") < url.indexOf(".")
    ? true
    : false;
const Card = ({
  imageClass,
  contentClass,
  imageUrl,
  title,
  content,
  link,
  isImageOnLeft,
  alt
}) => {
  const CardImage = () => {
    return (
      <div className={`col ${imageClass}`}>
        <img src={imageUrl} alt={alt} className="zigzag-image" />
      </div>
    );
  };

  const CardContent = () => {
    return (
      <div className={`col ${contentClass}`}>
        <h3>{title}</h3>
        <p>{content}</p>
        {link && link.trim() != "" && isUrlAbsolute(link) ? (
          <a href={link} className="item-link" target="_blank" rel="noreferrer">
            <img src="/assets/images/icon-arrow.png" alt="icon-arrow" />
          </a>
        ) : (
          <>
          {link && <LinkWithImage href={link}>
          <a href="#" className="item-link">
            <img src="/assets/images/icon-arrow.png" alt="icon-arrow" />
          </a>
          </LinkWithImage>}
          
          {/* <a href={link} className="item-link">
            <img src="/assets/images/icon-arrow.png" alt="icon-arrow" />
          </a> */}
          </>
        )}
      </div>
    );
  };

  return (
    <div className="row">
      {isImageOnLeft ? (
        <>
          <CardImage />
          <CardContent />
        </>
      ) : (
        <>
          <CardContent />
          <CardImage />
        </>
      )}
    </div>
  );
};

export default Card;
