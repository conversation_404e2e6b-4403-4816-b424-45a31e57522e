import React from "react";
import News from "../News";
import Link from "next/link";
import LinkComponent from "../../../internationalization/Link";

const FeaturedNews = ({ featuredNews, v }) => {
  const { items } = featuredNews;
  return (
    <section className="news-section">
      <div className="container">
        <div className="title flex-space-between">
          <h2>{v("featuredNews")}</h2>
          {/* <Link href="/news">
            <a href="#" className="link text-uppercase">
              {v("seeall")}
            </a>
          </Link> */}
          <LinkComponent
            href="/news/"
            linktext={v("seeall")}
            className="link text-uppercase"
          ></LinkComponent>
        </div>
        <div className="scroll">
          <div className="row news">
            {items[0]?.blogsCollection?.items?.map((item, index) => {
              return (
                item && <React.Fragment key={index}>
                  <News
                    key={index}
                    imageUrl={item.blogImage.url}
                    title={item.tags}
                    content={item.title}
                    url={`/blog/${item.slug}/`}
                  />
                </React.Fragment>
              );
            })}
          </div>
        </div>
      </div>
    </section>
  );
};

export default FeaturedNews;
