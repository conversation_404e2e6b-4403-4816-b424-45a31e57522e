import Head from "next/head";
import i18nextConfig from "../../../next-i18next.config";

const HeadMeta = ({ meta }) => {
  const alternateUrls = i18nextConfig.i18n.locales.map((lng) => {
    var alternateUrl = "";
    if (meta?.canonicalUrl) {
      var url = new URL(meta?.canonicalUrl);
      var hostname = url.hostname;
      var params = url.pathname.split("/").filter(m => m != '');
      params.shift();
      var path = params.join("/");
      alternateUrl = hostname + "/" + lng + "/" + path + "/";
      alternateUrl = alternateUrl.replace(/\/\//g, "/");
      alternateUrl = "https://" + alternateUrl;
    }

    return (
      <link rel="alternate" href={alternateUrl} hrefLang={lng} key={lng} />
    );
  });

  return (
    <>
      <Head>
        <title>{meta?.title}</title>
        <meta name="description" content={meta?.description} />
        <meta name="keywords" content={meta?.keywords} />
        <meta name="robots" content={meta?.robots} />
        <link rel="canonical" href={meta?.canonicalUrl}></link>
        {alternateUrls}
        <link rel="icon" href="/assets/favicon.ico" />
        {/* <script
          dangerouslySetInnerHTML={{
            __html: `(function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
      new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
      j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
      'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
      })(window,document,'script','dataLayer','GTM-5LL6NP9');`,
          }}
        /> */}
      </Head>
    </>
  );
};

export default HeadMeta;
