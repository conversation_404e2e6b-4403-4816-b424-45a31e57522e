import styles from "./Jobs.module.scss";
import Link from "next/link";
import LinkComponent from "../../../internationalization/Link";
import React from "react";
const Jobs = ({ featuresSection, opportunitesSection }) => {
  return (
    <section className="jobs-section">
      <div className="container">
        <div className={`row ${styles["features"]}`}>
          {featuresSection?.items[0]?.featuresCollection?.items.map(
            (item, key) => {
              return (
                item && <React.Fragment key={key}>
                  <div className={`${styles["col"]} col-3`}>
                    <div className={styles.flex}>
                      <div className={styles.icon}>
                        <img src={item?.logo?.url} width="43" alt={item?.logo?.description} />
                      </div>
                      <h3>
                        {/* <Link href={item.link}>
                          <a>{item.title}</a>
                        </Link> */}
                        <LinkComponent
                          href={item?.link}
                          linktext={item?.title}
                        ></LinkComponent>
                      </h3>
                    </div>
                    <p>{item?.description}</p>
                  </div>
                </React.Fragment>
              );
            }
          )}
        </div>
        <div className={`row ${styles["opportunities"]}`}>
          {opportunitesSection?.items?.map((item, key) => {
            return (
              item && <React.Fragment key={key}>
                <div className={`${styles["col"]} col-6`}>
                  <Link href={item?.url}>
                    <a href="#">
                      <img src={item?.image?.url} alt={item?.image?.description} />
                      <div className={styles["link-text"]}>
                        <h2>{item.title}</h2>
                        <p className="link-icon"></p>
                      </div>
                    </a>
                  </Link>
                </div>
              </React.Fragment>
            );
          })}
        </div>
      </div>
    </section>
  );
};

export default Jobs;
