import AccordionItem from "../AccordianItem";
import styles from "./Accordian.module.scss";


const Accordion = ({faqs}) => {
  
  return (
    faqs && faqs.items.length &&
    <>
      <div className={`${styles["faq-section"]} container`}>
        <h2 className={styles["title"]}>JFK T8 Concessions FAQ</h2>
        <ul className={`${styles["accordion"]}`}>
          {faqs.items.map((faq, index) => (
            faq && <AccordionItem key={index} faq={faq} />
          ))}
        </ul>
      </div>
      <div className={styles["faq-section"]}></div>
      </>
  );
};

export default Accordion;
