import { useState, useRef } from "react";
import styles from './AccordianItem.module.scss'
import { documentToReactComponents} from "@contentful/rich-text-react-renderer"
const AccordionItem = ({ faq }) => {

  const [clicked, setClicked] = useState(false);
  const contentEl = useRef();



  const { question, answer } = faq;



  const handleToggle = () => {
    setClicked((prev) => !prev);
  };

  return (
    <li className={`${clicked ? styles["active"] : ""} ${styles["accordion_item"]}`}>
      <button className={styles["button"]} onClick={handleToggle}>
        {question}
        <span className={styles["control"]}>{clicked ? "—" : "+"} </span>
      </button>

      <div
        ref={contentEl}
        className={styles["answer_wrapper"]}
        style={
          clicked
            ? { height: contentEl.current.scrollHeight }
            : { height: "0px" }
        }
      >
        <div className={styles["answer"]}>{documentToReactComponents(answer.json)}</div>
      </div>
    </li>
  );
};


export default AccordionItem;
