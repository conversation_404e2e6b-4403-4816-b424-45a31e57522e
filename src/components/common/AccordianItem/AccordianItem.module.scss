.accordion_item {

    margin: 5px 0;

    .button {
        font-size: 16px;
        background-color: #f5f5f5;
        font-weight: 700;
        display: flex;
        width: 100%;
        justify-content: space-between;
        align-items: center;
        padding: 14px 18px;
        border: none;
        text-align: left;
        cursor: pointer;
    }
    &.active {
		.button {
			background-color: #2071c5;
            color: #fff;
		}
	}
    .control {
        font-size: 20px;
        padding-left: 10px;
    }
    .answer {
        // background-color: #f5f5f5;
        padding: 14px 18px;
    }
    .answer_wrapper {
        height: 0;
        overflow: hidden;
        transition: height ease 0.2s;
    }


}

