.event_section {
  padding: 0 0 70px 0;

  @media screen and (min-width: 768px) {
    .row {
      display: flex;
      min-height: 446px;
    }
  }
}

.event_card,
.event_list {
  position: relative;
  display: flex;

  .event-img {
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    padding-bottom: 100%;
    background-color: black;

    >a>img {
      width: 100%;
      height: 100%;
      object-fit: cover;
      opacity: 0.8;
    }
  }

  .event-arrow {
    background-color: var(--gray-dr);
    position: absolute;
    right: 0;
    bottom: 0;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.4s ease;

    img {
      width: 20px;
    }
  }

  .event-info {
    h3 {
      font-size: 17px;
      line-height: 1.4;
      margin: 0;
      max-height: 51px;
      display: block;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
      overflow: hidden;
      text-overflow: ellipsis;
    }

    h4 {
      opacity: 0.6;
      font-size: 13px;
      margin: 0 0 10px;
    }
  }

  &:hover {
    a {
      color: #000;
    }

    .event-arrow {
      background-color: var(--secondary);
    }
  }

  @media screen and (max-width: 767px) {
    .event-info {
      h3 {
        font-size: 15px;
        line-height: 1.3;
        max-height: 40px;
      }
    }

    .event-arrow {
      width: 32px;
      height: 32px;

      img {
        width: 18px;
      }
    }
  }
}

.event_card {
  height: 100%;
  min-height: 484px;
  @media screen and (max-width: 767px) {
    min-height: auto;
  }
  .event-img {
    .event-arrow {
      right: 40px;
      bottom: 40px;
      width: 40px;
      height: 40px;
    }
  }

  .event-info {
    background-color: white;
    position: absolute;
    left: 15px;
    right: 15px;
    bottom: 15px;
    padding: 15px;
    padding-right: 60px;
  }

  @media screen and (max-width: 767px) {
    height: auto;
    padding-bottom: 100%;
  }
}

.event_list {
  display: flex;
  padding: 32px 15px 0;

  &:first-child {
    padding-top: 0;
  }

  .event-img {
    width: 100%;
    height: 140px;
    max-width: 140px;
    padding: 0;
    position: relative;
  }

  .event-info {
    padding-left: 15px;
  }

  @media screen and (max-width: 767px) {
    padding: 15px 0;

    &:first-child {
      padding-top: 30px;
    }

    .event-img {
      height: 100px;
      max-width: 100px;
    }

  }
}