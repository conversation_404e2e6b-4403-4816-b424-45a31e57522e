import React from "react";
// import ReactDOM from 'react-dom'
import styles from "./Event.module.scss";
import SocialMediaIcons from "../SocialMediaIcons";
import LinkWithImage from "../../../internationalization/LinkWithImage";
import LinkComponent from "../../../internationalization/Link";
const Events = ({ events, v, l }) => {
  let eventList = [];



  const getCurrentEvents = () => {
    return events?.filter(x => {
      var now = new Date();
      // now = new Date(now.getTime() + now.getTimezoneOffset() * 60000)
      return new Date(x.date) >= now;
    })
  }

  let eventsToDisplay = getCurrentEvents()?.slice(0, 4) || [];
  if (eventsToDisplay && eventsToDisplay.length > 0) {
    for (let i = 1; i < eventsToDisplay.length; i++) {
      eventList.push(
        <React.Fragment key={i}>
          <div className={`${styles.event_list} flex`}>
            <div className={styles["event-img"]}>
              <LinkWithImage href={eventsToDisplay[i].eventLink}>
                <a href={eventsToDisplay[i].eventLink}>
                  <img
                    className="center-align"
                    src={eventsToDisplay[i]?.eventImage?.url}
                    alt={eventsToDisplay[i]?.eventImage?.description}
                  />
                  <span className={styles["event-arrow"]}>
                    <img src="/assets/images/icon-arrow-white.png" alt="icon-arrow-white" />
                  </span>
                </a>
              </LinkWithImage>
            </div>
            <div className={styles["event-info"]}>
              <h4>{getDate(eventsToDisplay[i]?.date)}</h4>
              <h3>
                <LinkComponent
                  href={eventsToDisplay[i].eventLink}
                  linktext={eventsToDisplay[i]?.title}
                ></LinkComponent>
              </h3>
              {/* <SocialMediaIcons eventUrl={eventBriteEvents?.events[i].url}/> */}
            </div>
          </div>
        </React.Fragment>
      );
    }
  }



  return (
    eventsToDisplay &&
    eventsToDisplay.length > 0 && (
      <section className={`${styles.event_section} event-section`}>
        <div className="container">
          <div className="title flex-space-between">
            <h2>{l("events")}</h2>
            <LinkComponent
              href="/events/"
              linktext={v("seeall")}
              className="link text-uppercase"
            ></LinkComponent>
          </div>
          <div className={`${styles.row} row`}>
            <div className="col col-6">
              <div className={`${styles.event_card} event-card`}>
                <div className={styles["event-img"]}>
                  <LinkWithImage href={eventsToDisplay[0].eventLink}>
                    <a href={eventsToDisplay[0].eventLink}>
                      <img
                        className="center-align"
                        src={eventsToDisplay[0]?.eventImage?.url}
                        alt={eventsToDisplay[0]?.eventImage?.description}
                      />
                    </a>
                  </LinkWithImage>
                </div>
                <div className={styles["event-info"]}>
                  <h4>{getDate(eventsToDisplay[0]?.date)}</h4>
                  <h3>
                    <LinkComponent
                      href={eventsToDisplay[0]?.eventLink}
                      linktext={eventsToDisplay[0]?.title}
                    ></LinkComponent>
                    {/* <a href="#">{events[0].title}</a> */}
                  </h3>
                  <LinkWithImage href={eventsToDisplay[0]?.eventLink}>
                    <a href={eventsToDisplay[0]?.eventLink} className={styles["event-arrow"]}>
                      <img src="/assets/images/icon-arrow-white.png" alt="icon-arrow-white" />
                    </a>
                  </LinkWithImage>
                </div>
                {/* <SocialMediaIcons eventUrl={eventBriteEvents?.events[0]?.url} /> */}
              </div>
            </div>
            <div className="col col-6">{eventList}</div>
          </div>
        </div>
      </section>
    )
  );
};

const getDate = (date) => {
  var dateOptions = { year: "numeric", month: "long", day: "numeric", timeZone: 'America/New_York' };
  return new Date(date).toLocaleDateString("en-US", dateOptions);
}
// new Date(date)
//   .toLocaleTimeString("en-us", {
//     month: "short",
//     day: "numeric",
//     hour: "2-digit",
//     minute: "2-digit",
//   })
//   .split(",")
//   .join(" -");

export default Events;
