.inner-banner {
  background: url(/assets/images/banner-about.jpg) no-repeat center center;
  background-size: cover;
  padding: 55px 10px;
  position: relative;
  display: flex;
  align-items: center;
  min-height: 200px;
  text-shadow: 2px 1px 3px rgb(0 0 0 / 30%);

  img {
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    object-fit: cover;
  }

  h1,
  p {
    z-index: 1;
    position: relative;
    color: #fff;
    text-align: center;
  }

  h1 {
    font-weight: 300;
    max-width: 650px;
    margin: auto;
  }

  p {
    max-width: 790px;
    margin: 30px auto 0;
    font-size: 16px;
    font-weight: 100;
    &:empty {
      margin: 0;
    }
  }

  &:after {
    content: '';
    display: block;
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0;
    left: 0;
    background: rgb(29, 111, 197);
    opacity: 0.4;
  }
}

@media screen and (min-width: 1200px) {
  .inner-banner {
    min-height: 300px;
  }
}

@media (max-width: 991px) {
  .inner-banner h1 {
    font-size: 42px;
  }
}

@media screen and (max-width: 767px) {
  .inner-banner h1 {
    font-size: 28px;
  }
}
