import styles from './Banner.module.scss'
import { documentToReactComponents } from '@contentful/rich-text-react-renderer';
const Banner = ({sectionClass, details}) => {
  
  // const {title, heroImage, description } = details;

    var classes = sectionClass.split(" ").map(c => {
      return styles[c];
    }).join(" ");

    return(
      details && <section className={classes}>
          <img src={details?.heroImage?.url} alt={details?.heroImage?.description} />
          <div className="container">
            {documentToReactComponents(details?.title?.json)}
            { details?.description && <p>{details?.description}</p> }
          </div>
        </section>
    )
}

export default Banner;