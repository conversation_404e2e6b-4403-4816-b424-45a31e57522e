import { Redirect } from "../internationalization/redirect";
export default Redirect;
// import HeadMeta from "../src/components/HeadMeta";
// import ContentfulApi from "../contentful/contentfulApi";
// import MyT8 from "../src/pages/MyT8";
// const myT8 = ({ myT8HeroImage, myT8Features, myT8Stories,featuredNews }) => {
//   return (
//     <>
//       <HeadMeta title="My T8" />
//       <MyT8
//         myT8HeroImage={myT8HeroImage}
//         myT8Features={myT8Features}
//         myT8Stories={myT8Stories}
//         featuredNews={featuredNews}
//       />
//     </>
//   );
// };

// export async function getStaticProps() {
//   const featuredNews = await ContentfulApi.GetFeaturedNews();
//   const topPartnerLogos = await ContentfulApi.GetHeaderTopPartners();
//   const footerPartnerLogos = await ContentfulApi.GetFooterPartnerLogos();
//   const footerCommunityPartnerLogos =
//     await ContentfulApi.GetFooterCommunityPartnerLogos();
//   const myT8HeroImage = await ContentfulApi.GetMyT8HeroImage();
//   const myT8Features = await ContentfulApi.GetMyT8Features();
//   const myT8Stories = await ContentfulApi.GetMyT8Stories();
//   return {
//     props: {
//       featuredNews,
//       topPartnerLogos,
//       footerPartnerLogos,
//       footerCommunityPartnerLogos,
//       myT8HeroImage: myT8HeroImage.items[0],
//       myT8Features,
//       myT8Stories,
//     },
//   };
// }

// export default myT8;
