import { Redirect } from "../internationalization/redirect";
export default Redirect;
// import HeadMeta from "../src/components/HeadMeta";
// import EmploymentOpportunities from "../src/pages/Employment-Opportunities";
// import ContentfulApi from "../contentful/contentfulApi";
// const EmploymentOpportunitiesPage = ({ featuredNews, employmentoppHero,textSection }) => {
//   return (
//     <>
//       <HeadMeta title="Employment Opportunities" />
//       <EmploymentOpportunities
//         featuredNews={featuredNews}
//         employmentoppHero={employmentoppHero}
//         textSection={textSection}
//       />
//     </>
//   );
// };

// export async function getStaticProps() {
//   const featuredNews = await ContentfulApi.GetFeaturedNews();
//   const employmentoppHero =
//     await ContentfulApi.GetEmploymentOpportunitiesHeroImage();
//   const topPartnerLogos = await ContentfulApi.GetHeaderTopPartners();
//   const footerPartnerLogos = await ContentfulApi.GetFooterPartnerLogos();
//   const footerCommunityPartnerLogos =
//     await ContentfulApi.GetFooterCommunityPartnerLogos();
//   const textSection = await ContentfulApi.GetEmploymentOpportunitiesTextSection();
//   return {
//     props: {
//       featuredNews,
//       employmentoppHero: employmentoppHero.items[0],
//       topPartnerLogos,
//       footerPartnerLogos,
//       footerCommunityPartnerLogos,
//       textSection
//     },
//   };
// }

// export default EmploymentOpportunitiesPage;
