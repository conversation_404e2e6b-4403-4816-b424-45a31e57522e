import { Redirect } from "../internationalization/redirect";
export default Redirect;
// import HeadMeta from "../src/components/HeadMeta";
// import Updates from "../src/pages/Updates";
// import ContentfulApi from "../contentful/contentfulApi";
// const UpdatesPage = ({ updatesHero, blogs }) => {
//   return (
//     <>
//       <HeadMeta title="News" />
//       <Updates updatesHero={updatesHero} blogs={blogs} />
//     </>
//   );
// };

// export async function getStaticProps() {
//   const updatesHero = await ContentfulApi.GetUpdatesHeroImage();
//   const topPartnerLogos = await ContentfulApi.GetHeaderTopPartners();
//   const footerPartnerLogos = await ContentfulApi.GetFooterPartnerLogos();
//   const footerCommunityPartnerLogos =
//     await ContentfulApi.GetFooterCommunityPartnerLogos();
//   const blogs = await ContentfulApi.GetBlogs();
//   const blogsWithQuoteAndImage =
//     await ContentfulApi.GetBlogsWithQuotesAndImages();
//   let allBlogs = [...blogs?.items, ...blogsWithQuoteAndImage?.items];
//   const myT8Stories = await ContentfulApi.GetMyT8Stories();

//   // Filter myT8 blogs to not show on News Page
//   allBlogs = allBlogs.filter((item1) => {
//     return !myT8Stories.items.find((item2) => {
//       return item1.title === item2.title;
//     });
//   });

//   return {
//     props: {
//       updatesHero: updatesHero.items[0],
//       topPartnerLogos,
//       footerPartnerLogos,
//       footerCommunityPartnerLogos,
//       blogs: allBlogs,
//     },
//   };
// }

// export default UpdatesPage;
