import "../styles/globals.scss";
import "../styles/slick.scss";
import Layout from "../src/components/Layout";
const { ToastContainer } = require("react-toastify");
import "react-toastify/dist/ReactToastify.css";
import EmailUpdatesProvider from "../context/EmailUpdates/EmailUpdatesProvider";
import { appWithTranslation } from "next-i18next";
import { useTranslation } from "next-i18next";
import nextI18NextConfig from "../next-i18next.config";
import TagManager from "react-gtm-module";
import PreviewBanner from "../src/components/PreviewBanner";
import { useEffect } from "react";

function MyApp({ Component, pageProps }) {
  const tagManagerArgs = {
    gtmId: process.env.NEXT_PUBLIC_GTM_ID,
  };

  useEffect(() => {
    TagManager.initialize(tagManagerArgs);
  }, []);

  const { t } = useTranslation(["layout"]);

  const navLinksText = {
    about: t("about"),
    opportunities: t("opportunities"),
    businessOpportunities: t("business-opportunities"),
    employmentOpportunities: t("employment-opportunities"),
    community: t("community"),
    news: t("news"),
    contactus: t("contactus"),
    art: t("art"),
    events: t("events"),
    myT8: t("myT8"),
    exploreCurrentTerminal: t("exploreCurrentTerminal"),
    localBusinessAccelerater: t("localBusinessAccelerater"),
    locale: pageProps?._nextI18Next?.initialLocale,
  };

  const footerLinksText = {
    copyright: t("copyright"),
    privacyPolicy: t("privacy-policy"),
    termsOfUse: t("terms-of-use"),
    commercialDevelopment: t("commercial-development"),
    communityPartners: t("community-partners"),
    urwAirports: t("urw-airports"),
  };

  return (
    <>
      {/* <noscript
        dangerouslySetInnerHTML={{
          __html: `<iframe src="https://www.googletagmanager.com/ns.html?id=GTM-5LL6NP9" height="0" width="0" style="display:none;visibility:hidden"></iframe>`,
        }}
      /> */}
      {pageProps.isPreviewMode && <PreviewBanner />}

      <EmailUpdatesProvider>
        <Layout
          isHome={pageProps.isHome}
          heroImages={pageProps.heroImages}
          topPartnerLogos={pageProps.topPartnerLogos}
          footerPartnerLogos={pageProps.footerPartnerLogos}
          footerCommunityPartnerLogos={pageProps.footerCommunityPartnerLogos}
          navLinksText={navLinksText}
          footerLinksText={footerLinksText}
        >
          <Component {...pageProps} />
          <ToastContainer position="top-right" hideProgressBar={true} newestOnTop={false} draggable={false} pauseOnVisibilityChange closeOnClick pauseOnHover />
        </Layout>
      </EmailUpdatesProvider>

    </>
  );
}

export const getServerSideProps = async ({ locale }) => ({
  props: {
    ...(await serverSideTranslations(locale, ["layout"])),
  },
});

export default appWithTranslation(MyApp, nextI18NextConfig);
