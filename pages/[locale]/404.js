import { getStaticPaths } from "../../internationalization/getStaticPaths";
import ContentfulApi from "../../contentful/contentfulApi";
import LinkComponent from "../../internationalization/Link";
import { serverSideTranslations } from "next-i18next/serverSideTranslations";
import { useTranslation } from "next-i18next";
import Head from "next/head";
export default function PageNotFound() {
  const { t: v } = useTranslation(["common"]);

  return (
    <>
      <Head>
        <title>{v("pagenotfound")}</title>
      </Head>
        <div className="page-404">
          <div className="img-404">
            <img src="/assets/images/404.png" alt="" width="400" />
          </div>
          <LinkComponent
            href="/"
            linktext={v("backtohome")}
          ></LinkComponent>
        </div>
    </>
  );
}

async function getStaticProps(context) {
  const isPreviewMode = context.preview || false;

  const topPartnerLogos = await ContentfulApi.GetHeaderTopPartners(isPreviewMode);
  const footerPartnerLogos = await ContentfulApi.GetFooterPartnerLogos(isPreviewMode);
  const footerCommunityPartnerLogos =
    await ContentfulApi.GetFooterCommunityPartnerLogos(isPreviewMode);

  return {
    props: {
      topPartnerLogos,
      footerPartnerLogos,
      footerCommunityPartnerLogos,
      isPreviewMode,
      ...(await serverSideTranslations(context.params.locale, ["layout", "common"])),
    },
  };
}

export { getStaticPaths, getStaticProps };
