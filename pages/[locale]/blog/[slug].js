import ContentfulApi from "../../../contentful/contentfulApi";
import Blog from "../../../src/components/Blog";
import BlogWithQuotesAndImages from "../../../src/components/BlogWithQuotesAndImages";
import { serverSideTranslations } from "next-i18next/serverSideTranslations";
import i18nextConfig from '../../../next-i18next.config'
export const getStaticPaths = async () => {

  const blogs = await ContentfulApi.GetBlogs();
  const blogsWithQuoteAndImage =
    await ContentfulApi.GetBlogsWithQuotesAndImages();
  const allBlogs = [...blogs?.items, ...blogsWithQuoteAndImage?.items];
 
//   const paths = allBlogs.map((item) => {
//     return {
//       params: { slug: item.slug },
//     };
//   });

  const paths = [];
  i18nextConfig.i18n.locales.map((locale) => {
    return allBlogs.map((item) => {
      return paths.push({ params: { slug: item.slug, locale: locale } });
    });
  });


  return {
    paths,
    fallback: false,
  };
};

export async function getStaticProps(context) {
  const isPreviewMode = context.preview || false;
  const blogs = await ContentfulApi.GetBlogsByFields(context.params.slug, context.params.locale,isPreviewMode);
  const blogsWithQuoteAndImage =
    await ContentfulApi.GetBlogsByFieldsWithQuotesAndImages(context.params.slug,context.params.locale,isPreviewMode);

  const blog = blogs.items.length
    ? blogs.items[0]
    : blogsWithQuoteAndImage.items[0];
  const topPartnerLogos = await ContentfulApi.GetHeaderTopPartners(isPreviewMode);
  const footerPartnerLogos = await ContentfulApi.GetFooterPartnerLogos(isPreviewMode);
  const footerCommunityPartnerLogos =
    await ContentfulApi.GetFooterCommunityPartnerLogos(isPreviewMode);

  const { previous, next } = await getPreviousAndNextSlug(context.params.slug, context.params.locale,isPreviewMode);
  const locale = context.params.locale;
  return {
    props: {
      blog: blog,
      previous,
      locale,
      next,
      topPartnerLogos,
      footerPartnerLogos,
      footerCommunityPartnerLogos,
      isPreviewMode,
      ...(await serverSideTranslations(context.params.locale, ["layout","common"])),
    },
  };
}

async function getPreviousAndNextSlug(slug,locale,isPreviewMode) {
  const slugs = await ContentfulApi.GetSlugsForPagination(locale,isPreviewMode);
  const postIndex = slugs.findIndex((postHeader) => postHeader?.slug === slug);
  return {
    previous: postIndex <= 0 ? null : { slug: slugs[postIndex - 1]?.slug },
    next:
      postIndex >= slugs.length - 1
        ? null
        : { slug:  slugs[postIndex + 1]?.slug },
  };
}

export default function BlogDetails({ blog, previous, next, locale }) {
  return (
    <>
      
      {blog?.isBlogWithQuoteAndImage == "Yes" ? (
        <BlogWithQuotesAndImages blog={blog} previous={previous} next={next} locale={locale}  />
      ) : (
        <Blog blog={blog} previous={previous} next={next} locale={locale} />
      )}
    </>
  );
}
