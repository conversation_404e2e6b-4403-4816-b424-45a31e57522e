import HeadMeta from "../../src/components/HeadMeta";
import Opportunities from "../../src/pages/business-opportunities";
import ContentfulApi from "../../contentful/contentfulApi";
import { getStaticPaths } from "../../internationalization/getStaticPaths";
import { serverSideTranslations } from "next-i18next/serverSideTranslations";
import { useTranslation } from "next-i18next";
export default function OpportunitiesPage({
  featuredNews,
  businessoppHero,
  textSection,
  meta,
  locale
}) {
  const { t } = useTranslation(["businessOpportunities"]);
  const { t:l } = useTranslation(["layout"]);
  const { t:v } = useTranslation(["common"]);

  return (
    <>
      <HeadMeta meta={meta} />
      <Opportunities
        featuredNews={featuredNews}
        businessoppHero={businessoppHero}
        textSection={textSection}
        locale={locale}
        t={t}
        v={v}
      />
    </>
  );
}

async function getStaticProps(context) {
  const isPreviewMode = context.preview || false;
  const featuredNews = await ContentfulApi.GetFeaturedNews(context.params.locale,isPreviewMode);
  const businessoppHero = await ContentfulApi.GetBusinessOpportunitiesHeroImage(context.params.locale,isPreviewMode);
  const topPartnerLogos = await ContentfulApi.GetHeaderTopPartners(isPreviewMode);
  const footerPartnerLogos = await ContentfulApi.GetFooterPartnerLogos(isPreviewMode);
  const footerCommunityPartnerLogos =
    await ContentfulApi.GetFooterCommunityPartnerLogos(isPreviewMode);
  const textSection = await ContentfulApi.GetBusinessOpportunitiesTextSection(context.params.locale,isPreviewMode);
  const meta = await ContentfulApi.GetBusinessOpportunitiesPageMeta(context.params.locale,isPreviewMode);
  const locale = context.params.locale;
  return {
    props: {
      featuredNews,
      businessoppHero: businessoppHero?.items[0] || null,
      topPartnerLogos,
      footerPartnerLogos,
      footerCommunityPartnerLogos,
      textSection,
      meta,
      locale,
      isPreviewMode,
      ...(await serverSideTranslations(context.params.locale, [
        "businessOpportunities",
        "layout",
        "common"
      ])),
    },
  };
}

export { getStaticPaths, getStaticProps };
