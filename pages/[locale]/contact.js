import HeadMeta from "../../src/components/HeadMeta";
import Contact from "../../src/pages/Contact";
import ContentfulApi from "../../contentful/contentfulApi";
import { getStaticPaths } from "../../internationalization/getStaticPaths";
import { serverSideTranslations } from "next-i18next/serverSideTranslations";
import { useTranslation } from "next-i18next";
export default function ContactPage({ contactHero, opportunitesSection, locale, meta }) {
  const { t } = useTranslation(["contact"]);
  const { t: m } = useTranslation(["stayuptodate"]);
  const { t: l } = useTranslation(["layout"]);
  const { t:v } = useTranslation(["common"]);

  const stayUptoDateText = {
    stayUptoDate: m("stayUpToDate"),
    submit: m("submit"),
    enterEmail: m("enterEmail"),
    validEmail: m("validEmail"),
    errorMessage: m("errorMessage"),
  };

  return (
    <>
      <HeadMeta meta={meta} />
      <Contact
        contactHero={contactHero}
        opportunitesSection={opportunitesSection}
        stayUptoDateText={stayUptoDateText}
        locale={locale}
        t={t}
        v={v}
        m={m}
      />
    </>
  );
}

async function getStaticProps(context) {
  const isPreviewMode = context.preview || false;
  const contactHero = await ContentfulApi.GetContactHeroImage(context.params.locale,isPreviewMode);
  const topPartnerLogos = await ContentfulApi.GetHeaderTopPartners(isPreviewMode);
  const footerPartnerLogos = await ContentfulApi.GetFooterPartnerLogos(isPreviewMode);
  const footerCommunityPartnerLogos =
    await ContentfulApi.GetFooterCommunityPartnerLogos(isPreviewMode);
  const opportunitesSection = await ContentfulApi.GetOpportunitiesBannerSection(context.params.locale,isPreviewMode);
  const meta = await ContentfulApi.GetContactPageMeta(context.params.locale,isPreviewMode);

  const locale = context.params.locale;
  return {
    props: {
      contactHero: contactHero?.items[0] || null,
      topPartnerLogos,
      footerPartnerLogos,
      footerCommunityPartnerLogos,
      opportunitesSection,
      meta,
      locale,
      isPreviewMode,
      ...(await serverSideTranslations(context.params.locale, [
        "contact",
        "stayuptodate",
        "layout",
        "common"
      ])),
    },
  };
}

export { getStaticPaths, getStaticProps };
