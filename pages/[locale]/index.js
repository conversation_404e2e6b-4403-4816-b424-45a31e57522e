import HeadMeta from "../../src/components/HeadMeta";
import Home from "../../src/pages/Home";
import ContentfulApi from "../../contentful/contentfulApi";
import { getStaticPaths } from "../../internationalization/getStaticPaths";
import { serverSideTranslations } from "next-i18next/serverSideTranslations";
import { useTranslation } from "next-i18next";
export default function HomePage({
  featuredNews,
  homeFeaturesSection,
  opportunitesSection,
  events,
  meta,
  locale
}) {
  const { t:m } = useTranslation(["stayuptodate"]);
  const { t:l } = useTranslation(["layout"]);
  const { t:v } = useTranslation(["common"]);

  const stayUptoDateText = {
    stayUptoDate: m("stayUpToDate"),
    submit: m("submit"),
    enterEmail: m("enterEmail"),
    validEmail : m("validEmail"),
    errorMessage : m("errorMessage")
  };

  return (
    <>
      <HeadMeta meta={meta} />
      <Home
        featuredNews={featuredNews}
        homeFeaturesSection={homeFeaturesSection}
        opportunitesSection={opportunitesSection}
        stayUptoDateText={stayUptoDateText}
        events={events}
        locale={locale}
        m={m}
        v={v}
        l={l}
      />
    </>
  );
}

async function getStaticProps(context) {
  const isPreviewMode = context.preview || false;

  const featuredNews = await ContentfulApi.GetFeaturedNews(context.params.locale,isPreviewMode);
  const heroImages = await ContentfulApi.GetHeroImagesForHome(context.params.locale,isPreviewMode);
  const topPartnerLogos = await ContentfulApi.GetHeaderTopPartners(isPreviewMode);
  const footerPartnerLogos = await ContentfulApi.GetFooterPartnerLogos(isPreviewMode);
  const homeFeaturesSection = await ContentfulApi.GetHomePageFeatures(context.params.locale,isPreviewMode);
  const footerCommunityPartnerLogos =
    await ContentfulApi.GetFooterCommunityPartnerLogos(isPreviewMode);
  const opportunitesSection =
    await ContentfulApi.GetOpportunitiesBannerSection(context.params.locale,isPreviewMode);
  const events = await ContentfulApi.GetEvents(context.params.locale,isPreviewMode);
  // const eventBriteEvents = await EventBriteApi.getAllEvents();
  const meta = await ContentfulApi.GetHomePageMeta(context.params.locale,isPreviewMode);
    const locale = context.params.locale;
  return {
    props: {
      isHome: true,
      featuredNews,
      heroImages,
      topPartnerLogos,
      footerPartnerLogos,
      footerCommunityPartnerLogos,
      homeFeaturesSection,
      opportunitesSection,
      events,
      // eventBriteEvents,
      meta,
      locale,
      isPreviewMode,
      ...(await serverSideTranslations(context.params.locale, ["layout", "stayuptodate","common"])),
    },
  };
}

export { getStaticPaths, getStaticProps };
