import HeadMeta from "../../src/components/HeadMeta";
import ArtUpdates from "../../src/pages/ArtUpdates";
import ContentfulApi from "../../contentful/contentfulApi";
import { getStaticPaths } from "../../internationalization/getStaticPaths";
import { serverSideTranslations } from "next-i18next/serverSideTranslations";
import { useTranslation } from "next-i18next";
export default function UpdatesPage({ updatesHero, blogs, meta, artBackgroundImage }) {

  const { t } = useTranslation(["layout"]);
  const { t: v } = useTranslation(["common"]);

  return (
    <>
      <HeadMeta meta={meta} />
      <div style={{
        background: `url("${artBackgroundImage}") repeat 0 0`,
        backgroundSize: `350px`,
        backgroundAttachment: `fixed`
      }}>
        <ArtUpdates updatesHero={updatesHero} blogs={blogs} v={v} />
      </div>
    </>
  );
};

async function getStaticProps(context) {
  const isPreviewMode = context.preview || false;

  const artHero = await ContentfulApi.GetArtHeroImage(context.params.locale, isPreviewMode);
  const artBackgroundImage = await ContentfulApi.GetArtBackground(isPreviewMode);

  const topPartnerLogos = await ContentfulApi.GetHeaderTopPartners(isPreviewMode);
  const footerPartnerLogos = await ContentfulApi.GetFooterPartnerLogos(isPreviewMode);
  const footerCommunityPartnerLogos =
    await ContentfulApi.GetFooterCommunityPartnerLogos(isPreviewMode);
  const artBlogs = await ContentfulApi.GetArtBlogs(context.params.locale, isPreviewMode);
  const artBlogsWithQuoteAndImage =
    await ContentfulApi.GetArtBlogsWithQuotesAndImages(context.params.locale, isPreviewMode);
  let allBlogs = [...artBlogs?.items, ...artBlogsWithQuoteAndImage?.items];
  const myT8Stories = await ContentfulApi.GetMyT8Stories(context.params.locale, isPreviewMode);
  const meta = await ContentfulApi.GetNewsPageMeta(context.params.locale, isPreviewMode);

  // Filter myT8 blogs to not show on News Page
  allBlogs = allBlogs.filter((item1) => {
    return !myT8Stories.items.find((item2) => {
      return item1.title === item2.title;
    });
  });

  return {
    props: {
      updatesHero: artHero?.items[0] || null,
      artBackgroundImage: artBackgroundImage?.items[0]?.backgroundImage.url || null,
      topPartnerLogos,
      footerPartnerLogos,
      footerCommunityPartnerLogos,
      blogs: allBlogs,
      meta,
      isPreviewMode,
      ...(await serverSideTranslations(context.params.locale, ["layout", "common"]))
    },
  };
}

export { getStaticPaths, getStaticProps };
