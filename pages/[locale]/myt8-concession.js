import MyT8Form from "../../src/components/Forms/MyT8";
import ContentfulApi from "../../contentful/contentfulApi";
import { getStaticPaths } from "../../internationalization/getStaticPaths";
import { serverSideTranslations } from "next-i18next/serverSideTranslations";
import { useTranslation } from "next-i18next";
import HeadMeta from "../../src/components/HeadMeta";

export default function MyT8Concession({ locale, meta }) {
  const { t: v } = useTranslation(["common"]);
  const { t } = useTranslation(["myT8"]);

  return (
    <>
      <HeadMeta meta={meta} />
      <MyT8Form v={v} t={t} locale={locale} />;
    </>
  );
}

async function getStaticProps(context) {
  const isPreviewMode = context.preview || false;

  const topPartnerLogos = await ContentfulApi.GetHeaderTopPartners(
    isPreviewMode
  );
  const footerPartnerLogos = await ContentfulApi.GetFooterPartnerLogos(
    isPreviewMode
  );
  const footerCommunityPartnerLogos =
    await ContentfulApi.GetFooterCommunityPartnerLogos(isPreviewMode);
   
  const meta = await ContentfulApi.GetMyT8ConcessionPageMeta(context.params.locale,isPreviewMode);  
  return {
    props: {
      topPartnerLogos,
      footerPartnerLogos,
      footerCommunityPartnerLogos,
      isPreviewMode,
      meta,
      locale: context.params.locale,
      ...(await serverSideTranslations(context.params.locale, [
        "layout",
        "common",
        "myT8",
      ])),
    },
  };
}

export { getStaticPaths, getStaticProps };
