import HeadMeta from "../../src/components/HeadMeta";
import Community from "../../src/pages/Community";
import ContentfulApi from "../../contentful/contentfulApi";
import { getStaticPaths } from "../../internationalization/getStaticPaths";
import { serverSideTranslations } from "next-i18next/serverSideTranslations";
import { useTranslation } from "next-i18next";
export default function CommunityPage ({
  featuredNews,
  communityPartners,
  communityHero,
  communityFeatures,
  meta
}) {
  const { t } = useTranslation(["layout"]);
  const { t:v } = useTranslation(["common"]);

  return (
    <>
      <HeadMeta meta={meta} />
      <Community
        featuredNews={featuredNews}
        communityPartners={communityPartners}
        communityHero={communityHero}
        communityFeatures={communityFeatures}
        v={v}
      />
    </>
  );
};

async function getStaticProps(context) {
  const isPreviewMode = context.preview || false;
  const communityPartners = await ContentfulApi.GetCommunityPartners(context.params.locale,isPreviewMode);
  const featuredNews = await ContentfulApi.GetFeaturedNews(context.params.locale,isPreviewMode);
  const communityHero = await ContentfulApi.GetCommunityHeroImage(context.params.locale,isPreviewMode);
  const topPartnerLogos = await ContentfulApi.GetHeaderTopPartners(isPreviewMode);
  const footerPartnerLogos = await ContentfulApi.GetFooterPartnerLogos(isPreviewMode);
  const footerCommunityPartnerLogos =
    await ContentfulApi.GetFooterCommunityPartnerLogos(isPreviewMode);
  const communityFeatures = await ContentfulApi.GetCommunityPageFeatures(context.params.locale,isPreviewMode);
  const meta = await ContentfulApi.GetCommunityPageMeta(context.params.locale,isPreviewMode);

  return {
    props: {
      featuredNews,
      communityPartners,
      communityHero: communityHero?.items[0] || null,
      topPartnerLogos,
      footerPartnerLogos,
      footerCommunityPartnerLogos,
      meta,
      communityFeatures,
      isPreviewMode,
      ...(await serverSideTranslations(context.params.locale, ["layout","common"]))
    },
  };
}

export { getStaticPaths, getStaticProps };
