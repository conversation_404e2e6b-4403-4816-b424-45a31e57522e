import HeadMeta from "../../src/components/HeadMeta";
import ContentfulApi from "../../contentful/contentfulApi";
import MyT8 from "../../src/pages/MyT8";
import { getStaticPaths } from "../../internationalization/getStaticPaths";
import { serverSideTranslations } from "next-i18next/serverSideTranslations";
import { useTranslation } from "next-i18next";


export default function MyT8Page({
  myT8HeroImage,
  myT8Features,
  myT8Stories,
  featuredNews,
  faqs,
  meta,
  locale
}) {
  const { t: v } = useTranslation(["common"]);
  const { t } = useTranslation(["myT8"]);


  return (
    <>
      <HeadMeta meta={meta} />

      <MyT8
        myT8HeroImage={myT8HeroImage}
        myT8Features={myT8Features}
        myT8Stories={myT8Stories}
        featuredNews={featuredNews}
        // toggleMyT8Form={toggleMyT8Form}
        faqs={faqs}
        v={v}
        t={t}
      />
      {/* { showMyT8Form && <MyT8Form v={v} t={t} toggleMyT8Form={toggleMyT8Form} locale={locale} />} */}
    </>
  );
}

async function getStaticProps(context) {
  const isPreviewMode = context.preview || false;

  const featuredNews = await ContentfulApi.GetFeaturedNews(
    context.params.locale,
    isPreviewMode
  );
  const topPartnerLogos = await ContentfulApi.GetHeaderTopPartners(
    isPreviewMode
  );
  const footerPartnerLogos = await ContentfulApi.GetFooterPartnerLogos(
    isPreviewMode
  );
  const footerCommunityPartnerLogos =
    await ContentfulApi.GetFooterCommunityPartnerLogos(isPreviewMode);
  const myT8HeroImage = await ContentfulApi.GetMyT8HeroImage(
    context.params.locale,
    isPreviewMode
  );
  const myT8Features = await ContentfulApi.GetMyT8Features(
    context.params.locale,
    isPreviewMode
  );
  const myT8Stories = await ContentfulApi.GetMyT8Stories(
    context.params.locale,
    isPreviewMode
  );
  const meta = await ContentfulApi.GetMyT8PageMeta(
    context.params.locale,
    isPreviewMode
  );

  const faqs = await ContentfulApi.GetFAQ(context.params.locale,isPreviewMode);

  return {
    props: {
      featuredNews,
      topPartnerLogos,
      footerPartnerLogos,
      footerCommunityPartnerLogos,
      myT8HeroImage: myT8HeroImage?.items[0] || null,
      myT8Features,
      myT8Stories,
      meta,
      faqs,
      isPreviewMode,
      locale:context.params.locale,
      ...(await serverSideTranslations(context.params.locale, [
        "layout",
        "common",
        "myT8",
      ])),
    },
  };
}

export { getStaticPaths, getStaticProps };
