import HeadMeta from "../../src/components/HeadMeta";
import EmploymentOpportunities from "../../src/pages/Employment-Opportunities";
import ContentfulApi from "../../contentful/contentfulApi";
import { getStaticPaths } from "../../internationalization/getStaticPaths";
import { serverSideTranslations } from "next-i18next/serverSideTranslations";
import { useTranslation } from "next-i18next";

export default function EmploymentOpportunitiesPage({
  featuredNews,
  employmentoppHero,
  textSection,
  meta,
  locale
}) {

  const { t } = useTranslation(["employmentOpportunities"]);
  const { t: l } = useTranslation(["layout"]);
  const { t:v } = useTranslation(["common"]);


  return (
    <>
      <HeadMeta meta={meta} />
      <EmploymentOpportunities
        featuredNews={featuredNews}
        employmentoppHero={employmentoppHero}
        textSection={textSection}
        locale={locale}
        t={t}
        v={v}
      />
    </>
  );
}

async function getStaticProps(context) {
  const isPreviewMode = context.preview || false;

  const featuredNews = await ContentfulApi.GetFeaturedNews(context.params.locale,isPreviewMode);
  const employmentoppHero =
    await ContentfulApi.GetEmploymentOpportunitiesHeroImage(context.params.locale,isPreviewMode);
  const topPartnerLogos = await ContentfulApi.GetHeaderTopPartners(isPreviewMode);
  const footerPartnerLogos = await ContentfulApi.GetFooterPartnerLogos(isPreviewMode);
  const footerCommunityPartnerLogos =
    await ContentfulApi.GetFooterCommunityPartnerLogos(isPreviewMode);
  const textSection =
    await ContentfulApi.GetEmploymentOpportunitiesTextSection(context.params.locale,isPreviewMode);
    const meta = await ContentfulApi.GetEmploymentOpportunitiesPageMeta(context.params.locale,isPreviewMode);

  const locale = context.params.locale;
  return {
    props: {
      featuredNews,
      employmentoppHero: employmentoppHero?.items[0] || null,
      topPartnerLogos,
      footerPartnerLogos,
      footerCommunityPartnerLogos,
      locale,
      meta,
      textSection,
      isPreviewMode,
      ...(await serverSideTranslations(context.params.locale, [
        "employmentOpportunities",
        "layout",
        "common"
      ])),    },
  };
}

export { getStaticPaths, getStaticProps };
