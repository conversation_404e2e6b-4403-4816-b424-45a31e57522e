import HeadMeta from "../../src/components/HeadMeta";
import Updates from "../../src/pages/Updates";
import ContentfulApi from "../../contentful/contentfulApi";
import { getStaticPaths } from "../../internationalization/getStaticPaths";
import { serverSideTranslations } from "next-i18next/serverSideTranslations";
import { useTranslation } from "next-i18next";
export default function UpdatesPage({ updatesHero, blogs, meta }) {

  const { t } = useTranslation(["layout"]);
  const { t: v } = useTranslation(["common"]);


  return (
    <>
      <HeadMeta meta={meta} />
      <Updates updatesHero={updatesHero} blogs={blogs} v={v} />
    </>
  );
};

async function getStaticProps(context) {
  const isPreviewMode = context.preview || false;

  const updatesHero = await ContentfulApi.GetUpdatesHeroImage(context.params.locale, isPreviewMode);
  const topPartnerLogos = await ContentfulApi.GetHeaderTopPartners(isPreviewMode);
  const footerPartnerLogos = await ContentfulApi.GetFooterPartnerLogos(isPreviewMode);
  const footerCommunityPartnerLogos =
    await ContentfulApi.GetFooterCommunityPartnerLogos(isPreviewMode);
  const blogs = await ContentfulApi.GetBlogs(context.params.locale, isPreviewMode);
  const blogsWithQuoteAndImage =
    await ContentfulApi.GetBlogsWithQuotesAndImages(context.params.locale, isPreviewMode);
  let allBlogs = [...blogs?.items, ...blogsWithQuoteAndImage?.items];
  const myT8Stories = await ContentfulApi.GetMyT8Stories(context.params.locale, isPreviewMode);
  const meta = await ContentfulApi.GetNewsPageMeta(context.params.locale, isPreviewMode);


  // Filter myT8 blogs to not show on News Page
  allBlogs = allBlogs.filter((item1) => {
    return !myT8Stories.items.find((item2) => {
      return item1.title === item2.title;
    });
  });


  // allBlogs = allBlogs.filter((blog) => {
  //   return !blog.secondaryTags?.some(x => x == "Art" || x == "Arte");
  // });

  return {
    props: {
      updatesHero: updatesHero?.items[0] || null,
      topPartnerLogos,
      footerPartnerLogos,
      footerCommunityPartnerLogos,
      blogs: allBlogs,
      meta,
      isPreviewMode,
      ...(await serverSideTranslations(context.params.locale, ["layout", "common"]))
    },
  };
}

export { getStaticPaths, getStaticProps };
