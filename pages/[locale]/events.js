import HeadMeta from "../../src/components/HeadMeta";
import ContentfulApi from "../../contentful/contentfulApi";
import { getStaticPaths } from "../../internationalization/getStaticPaths";
import { serverSideTranslations } from "next-i18next/serverSideTranslations";
import { useTranslation } from "next-i18next";
import Events from "../../src/pages/events";
import EventBriteApi from "../../eventbrite/eventbriteApi";

export default function EventsPage({ eventsHero, meta, events }) {

  const { t:v } = useTranslation(["common"]);

  return (
    <>
      <HeadMeta meta={meta} />
      <Events eventsHero={eventsHero} events={events} v={v} />
    </>
  );
}

async function getStaticProps(context) {
  const isPreviewMode = context.preview || false;
  const topPartnerLogos = await ContentfulApi.GetHeaderTopPartners(isPreviewMode);
  const footerPartnerLogos = await ContentfulApi.GetFooterPartnerLogos(isPreviewMode);
  const footerCommunityPartnerLogos =
    await ContentfulApi.GetFooterCommunityPartnerLogos(isPreviewMode);
  const eventsHero = await ContentfulApi.GetEventsHeroImage(context.params.locale,isPreviewMode);
  const meta = await ContentfulApi.GetEventPageMeta(context.params.locale,isPreviewMode);
  const events = await ContentfulApi.GetEvents(context.params.locale,isPreviewMode);
  // const eventBriteEvents = await EventBriteApi.getAllEvents();
  return {
    props: {
      topPartnerLogos,
      footerPartnerLogos,
      footerCommunityPartnerLogos,
      eventsHero : eventsHero || null,
      events,
      // eventBriteEvents,
      meta,
      isPreviewMode,
      ...(await serverSideTranslations(context.params.locale, ["layout", "common"])),
    },
  };
}

export { getStaticPaths, getStaticProps };
