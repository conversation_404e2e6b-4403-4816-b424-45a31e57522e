import HeadMeta from "../../src/components/HeadMeta";
import About from "../../src/pages/About";
import ContentfulApi from "../../contentful/contentfulApi";
import { getStaticPaths } from "../../internationalization/getStaticPaths";
import { serverSideTranslations } from "next-i18next/serverSideTranslations";
import { useTranslation } from "next-i18next";
export default function AboutPage({
  featuredNews,
  partners,
  aboutHero,
  aboutFeatures,
  aboutSliderImages,
  meta
}) {
  const { t } = useTranslation(["layout"]);
  const { t: v } = useTranslation(["common"]);

  return (
    <>
      <HeadMeta meta={meta} />
      <About
        featuredNews={featuredNews}
        partners={partners}
        aboutHero={aboutHero}
        aboutFeatures={aboutFeatures}
        aboutSliderImages={aboutSliderImages}
        v={v}
      />
    </>
  );
}

async function getStaticProps(context) {
  const isPreviewMode = context.preview || false;
  const featuredNews = await ContentfulApi.GetFeaturedNews(
    context.params.locale,
    isPreviewMode
  );
  const partners = await ContentfulApi.GetPartners(
    context.params.locale,
    isPreviewMode
  );
  const aboutHero = await ContentfulApi.GetAboutHeroImage(
    context.params.locale,
    isPreviewMode
  );
  const topPartnerLogos = await ContentfulApi.GetHeaderTopPartners(isPreviewMode);
  const footerPartnerLogos = await ContentfulApi.GetFooterPartnerLogos(isPreviewMode);
  const footerCommunityPartnerLogos =
    await ContentfulApi.GetFooterCommunityPartnerLogos(isPreviewMode);
  const aboutFeatures = await ContentfulApi.GetAboutPageFeatures(
    context.params.locale,
    isPreviewMode
  );
  const aboutSliderImages = await ContentfulApi.GetAboutSliderImages(isPreviewMode);
  const meta = await ContentfulApi.GetAboutPageMeta(
    context.params.locale,
    isPreviewMode
  );

  return {
    props: {
      featuredNews,
      partners,
      aboutHero: aboutHero?.items[0] || null,
      topPartnerLogos,
      footerPartnerLogos,
      footerCommunityPartnerLogos,
      aboutFeatures,
      aboutSliderImages,
      meta,
      isPreviewMode,
      ...(await serverSideTranslations(context.params.locale, [
        "layout",
        "common",
      ])),
    },
  };
}

export { getStaticPaths, getStaticProps };
