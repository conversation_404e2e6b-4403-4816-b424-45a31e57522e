import { Redirect } from "../internationalization/redirect";
export default Redirect;
// import HeadMeta from "../src/components/HeadMeta";
// import Contact from "../src/pages/Contact";
// import ContentfulApi from "../contentful/contentfulApi";
// const ContactPage = ({ contactHero, opportunitesSection }) => {
//   return (
//     <>
//       <HeadMeta title="Contact Us" />
//       <Contact
//         contactHero={contactHero}
//         opportunitesSection={opportunitesSection}
//       />
//     </>
//   );
// };

// export async function getStaticProps() {
//   const contactHero = await ContentfulApi.GetContactHeroImage();
//   const topPartnerLogos = await ContentfulApi.GetHeaderTopPartners();
//   const footerPartnerLogos = await ContentfulApi.GetFooterPartnerLogos();
//   const footerCommunityPartnerLogos =
//     await ContentfulApi.GetFooterCommunityPartnerLogos();
//   const opportunitesSection =
//     await ContentfulApi.GetOpportunitiesBannerSection();
//   return {
//     props: {
//       contactHero: contactHero.items[0],
//       topPartnerLogos,
//       footerPartnerLogos,
//       footerCommunityPartnerLogos,
//       opportunitesSection,
//     },
//   };
// }

// export default ContactPage;
