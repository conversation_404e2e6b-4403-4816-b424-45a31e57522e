import ContentfulApi from "../../contentful/contentfulApi";
export default async function handler(req, res) {
  const { redirectUrl, secret, slug } = req.query;

  if (secret != process.env.CONTENTFUL_PREVIEW_SECRET) {
    return res.status(401).json({ message: "Invalid Secret" });
  }

  let url = "/";

  if (!slug && !redirectUrl) {
    return res.status(401).json({ message: "Invalid slug or redirect url." });
  }

  if (redirectUrl && redirectUrl.trim() != "") {
    url = redirectUrl;
  } else if (slug && slug.trim() != "") {
    const blogs = await ContentfulApi.GetBlogsByFields(slug,"",true);
    const blogsWithQuoteAndImage =
      await ContentfulApi.GetBlogsByFieldsWithQuotesAndImages(slug,"",true);

    if (blogs.items.length == 0 && blogsWithQuoteAndImage.items.length == 0) {
      return res.status(401).json({ message: "Invalid slug" });
    }
    url = `/blog/${slug}`;
  }

  res.setPreviewData({});

  res.setHeader("Content-Type", "text/html");
  res.write(
    `<!DOCTYPE html><html><head><meta http-equiv="Refresh" content="0; url=${url}" />
    <script>window.location.href = '${url}'</script>
    </head>`
  );
  res.end();
}

// export default async function handler(req, res) {
//   res.setPreviewData({});
//   res.end();
// }
