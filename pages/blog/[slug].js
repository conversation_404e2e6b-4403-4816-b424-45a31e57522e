import { Redirect } from "../../internationalization/redirect";
export default Redirect;
// import ContentfulApi from "../../contentful/contentfulApi";
// import Blog from "../../src/components/Blog";
// import BlogWithQuotesAndImages from "../../src/components/BlogWithQuotesAndImages";
// export const getStaticPaths = async () => {
//   const blogs = await ContentfulApi.GetBlogs();
//   const blogsWithQuoteAndImage =
//     await ContentfulApi.GetBlogsWithQuotesAndImages();
//   const allBlogs = [...blogs?.items, ...blogsWithQuoteAndImage?.items];
//   const paths = allBlogs.map((item) => {
//     return {
//       params: { slug: item.slug },
//     };
//   });

//   return {
//     paths,
//     fallback: false,
//   };
// };

// export async function getStaticProps({ params }) {
//   const blogs = await ContentfulApi.GetBlogsByFields(params.slug);
//   const blogsWithQuoteAndImage =
//     await ContentfulApi.GetBlogsByFieldsWithQuotesAndImages(params.slug);

//   const blog = blogs.items.length
//     ? blogs.items[0]
//     : blogsWithQuoteAndImage.items[0];
//   const topPartnerLogos = await ContentfulApi.GetHeaderTopPartners();
//   const footerPartnerLogos = await ContentfulApi.GetFooterPartnerLogos();
//   const footerCommunityPartnerLogos =
//     await ContentfulApi.GetFooterCommunityPartnerLogos();

//   const { previous, next } = await getPreviousAndNextSlug(params.slug);

//   return {
//     props: {
//       blog: blog,
//       previous,
//       next,
//       topPartnerLogos,
//       footerPartnerLogos,
//       footerCommunityPartnerLogos,
//     },
//   };
// }

// async function getPreviousAndNextSlug(slug) {
//   const slugs = await ContentfulApi.GetSlugsForPagination();
//   const postIndex = slugs.findIndex((postHeader) => postHeader?.slug === slug);
//   return {
//     previous: postIndex <= 0 ? null : { slug: slugs[postIndex - 1]?.slug },
//     next:
//       postIndex >= slugs.length - 1
//         ? null
//         : { slug: slugs[postIndex + 1]?.slug },
//   };
// }

// export default function BlogDetails({ blog, previous, next }) {
//   return (
//     <>
      
//       {blog?.isBlogWithQuoteAndImage == "Yes" ? (
//         <BlogWithQuotesAndImages blog={blog} previous={previous} next={next} />
//       ) : (
//         <Blog blog={blog} previous={previous} next={next} />
//       )}
//     </>
//   );
// }
