import { Redirect } from "../internationalization/redirect";
export default Redirect;
// import HeadMeta from "../src/components/HeadMeta";
// import About from "../src/pages/About";
// import ContentfulApi from "../contentful/contentfulApi";
// const AboutPage = ({ featuredNews, partners, aboutHero, aboutFeatures,aboutSliderImages }) => {
//   return (
//     <>
//       <HeadMeta title="About Us" />
//       <About
//         featuredNews={featuredNews}
//         partners={partners}
//         aboutHero={aboutHero}
//         aboutFeatures={aboutFeatures}
//         aboutSliderImages={aboutSliderImages}
//       />
//     </>
//   );
// };

// export async function getStaticProps() {
//   const featuredNews = await ContentfulApi.GetFeaturedNews();
//   const partners = await ContentfulApi.GetPartners();
//   const aboutHero = await ContentfulApi.GetAboutHeroImage();
//   const topPartnerLogos = await ContentfulApi.GetHeaderTopPartners();
//   const footerPartnerLogos = await ContentfulApi.GetFooterPartnerLogos();
//   const footerCommunityPartnerLogos =
//     await ContentfulApi.GetFooterCommunityPartnerLogos();
//   const aboutFeatures = await ContentfulApi.GetAboutPageFeatures();
//   const aboutSliderImages = await ContentfulApi.GetAboutSliderImages();
//   return {
//     props: {
//       featuredNews,
//       partners,
//       aboutHero: aboutHero.items[0],
//       topPartnerLogos,
//       footerPartnerLogos,
//       footerCommunityPartnerLogos,
//       aboutFeatures,
//       aboutSliderImages
//     },
//   };
// }

// export default AboutPage;
