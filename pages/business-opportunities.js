import { Redirect } from "../internationalization/redirect";
export default Redirect;
// import HeadMeta from "../src/components/HeadMeta";
// import Opportunities from "../src/pages/Opportunities";
// import ContentfulApi from "../contentful/contentfulApi";
// const OpportunitiesPage = ({ featuredNews, businessoppHero,textSection }) => {
//   return (
//     <>
//       <HeadMeta title="Business Opportunities" />
//       <Opportunities
//         featuredNews={featuredNews}
//         businessoppHero={businessoppHero}
//         textSection={textSection}
//       />
//     </>
//   );
// };

// export async function getStaticProps() {
//   const featuredNews = await ContentfulApi.GetFeaturedNews();
//   const businessoppHero =
//     await ContentfulApi.GetBusinessOpportunitiesHeroImage();
//   const topPartnerLogos = await ContentfulApi.GetHeaderTopPartners();
//   const footerPartnerLogos = await ContentfulApi.GetFooterPartnerLogos();
//   const footerCommunityPartnerLogos =
//     await ContentfulApi.GetFooterCommunityPartnerLogos();
//   const textSection = await ContentfulApi.GetBusinessOpportunitiesTextSection();
//   return {
//     props: {
//       featuredNews,
//       businessoppHero: businessoppHero.items[0],
//       topPartnerLogos,
//       footerPartnerLogos,
//       footerCommunityPartnerLogos,
//       textSection
//     },
//   };
// }

// export default OpportunitiesPage;
