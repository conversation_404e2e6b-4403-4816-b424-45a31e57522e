import { Redirect } from "../internationalization/redirect";
export default Redirect;
// import HeadMeta from "../src/components/HeadMeta";
// import Community from "../src/pages/Community";
// import ContentfulApi from "../contentful/contentfulApi";
// const CommunityPage = ({
//   featuredNews,
//   communityPartners,
//   communityHero,
//   communityFeatures,
// }) => {
//   return (
//     <>
//       <HeadMeta title="Community" />
//       <Community
//         featuredNews={featuredNews}
//         communityPartners={communityPartners}
//         communityHero={communityHero}
//         communityFeatures={communityFeatures}
//       />
//     </>
//   );
// };

// export async function getStaticProps() {
//   const communityPartners = await ContentfulApi.GetCommunityPartners();
//   const featuredNews = await ContentfulApi.GetFeaturedNews();
//   const communityHero = await ContentfulApi.GetCommunityHeroImage();
//   const topPartnerLogos = await ContentfulApi.GetHeaderTopPartners();
//   const footerPartnerLogos = await ContentfulApi.GetFooterPartnerLogos();
//   const footerCommunityPartnerLogos =
//     await ContentfulApi.GetFooterCommunityPartnerLogos();
//   const communityFeatures = await ContentfulApi.GetCommunityPageFeatures();
//   return {
//     props: {
//       featuredNews,
//       communityPartners,
//       communityHero: communityHero.items[0],
//       topPartnerLogos,
//       footerPartnerLogos,
//       footerCommunityPartnerLogos,
//       communityFeatures,
//     },
//   };
// }

// export default CommunityPage;
