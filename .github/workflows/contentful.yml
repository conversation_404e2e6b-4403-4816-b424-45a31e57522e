name: Contentful

on:
  repository_dispatch:
    types:
      - demo-publish-event
      - prod-publish-event

concurrency:
  group: ${{ github.event.client_payload._environment }}
  cancel-in-progress: false      

jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3

      - name: Set up Node version
        uses: actions/setup-node@v3
        with:
          node-version: '22.x'

      - name: Set dev environment
        run: echo "_ENV=${{ github.event.client_payload._environment }}" >> $GITHUB_ENV
      
      - name: Fetch tags for Production deployment
        if: env._ENV == 'prod'
        run:  git fetch --tags origin
      
      - name: Set Latest Release Tag for Production deployment
        if: env._ENV == 'prod'
        run:  |
          echo "_LATEST_TAG=$(git tag | sort --version-sort | tail -n1)" >> $GITHUB_ENV 

      - name: Checkout code for release ${{ env._LATEST_TAG }}
        uses: actions/checkout@v3
        if: env._ENV == 'prod'
        with:
          ref: ${{ env._LATEST_TAG }}

      - name: Configure Awscli
        run:  make awscli
        env:
          _AWS_ACCESS_KEY_ID: ${{ secrets[format('{0}_AWS_ACCESS_KEY_ID', env._ENV)] }}
          _AWS_SECRET_ACCESS_KEY: ${{ secrets[format('{0}_AWS_SECRET_ACCESS_KEY', env._ENV)] }}

      - name: Install Build npm dependencies
        run:  make build-npm-install

      - name: Package Website and Contentful preview website Lambda artifacts
        run:  make package-website-and-contentful-preview-lambda-artifacts

      - name: Install tf and tg switch
        run: |
          rm -rf /usr/local/bin/terraform
          curl -L https://raw.githubusercontent.com/warrensbox/terraform-switcher/release/install.sh | sudo bash
          curl -L https://raw.githubusercontent.com/warrensbox/tgswitch/release/install.sh | sudo bash
          sudo chown -R $USER /usr/local/bin/

      - name: Setup Terraform plugin cache
        run:  make set-plugin-cache
  
      - name: Install and setup Terraform and Terragrunt version
        run:  make tf

      - name: Publish Lambda artifacts
        run:  make publish-website-lambda-artifacts

      - name: Deployment Plan - Website
        run:  make plan-deploy-website

      - name: Deployment - Website
        run:  make deploy-website

      - name: Invalidate Cloudfront cache
        run:  make invalidate-cache
