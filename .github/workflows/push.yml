name: Deploy

on:
  push:
    branches:
      - main
    tags:
      - 'v[0-9]+.[0-9]+.[0-9]+'

jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3

      - name: Set up Node version
        uses: actions/setup-node@v3
        with:
          node-version: '22.x'

      - name: Set dev environment
        if:  ${{ github.ref == 'refs/heads/main' }}
        run: echo "_ENV=demo" >> $GITHUB_ENV

      - name: Set prod environment
        if:  startsWith(github.ref, 'refs/tags/v')
        run: echo "_ENV=prod" >> $GITHUB_ENV

      - name: Configure Awscli
        run:  make awscli
        env:
          _AWS_ACCESS_KEY_ID: ${{ secrets[format('{0}_AWS_ACCESS_KEY_ID', env._ENV)] }}
          _AWS_SECRET_ACCESS_KEY: ${{ secrets[format('{0}_AWS_SECRET_ACCESS_KEY', env._ENV)] }}
      
      - name: Install Lambda npm dependencies
        run:  make lambda-npm-install

      - name: Package Lambda artifacts
        run:  make package-lambda-artifacts

      - name: Install Build npm dependencies
        run:  make build-npm-install

      - name: Package Website and Contentful preview Lambda artifacts
        run:  make package-website-and-contentful-preview-lambda-artifacts

      - name: Install tf and tg switch
        run: |
          rm -rf /usr/local/bin/terraform
          curl -L https://raw.githubusercontent.com/warrensbox/terraform-switcher/release/install.sh | sudo bash
          curl -L https://raw.githubusercontent.com/warrensbox/tgswitch/release/install.sh | sudo bash
          sudo chown -R $USER /usr/local/bin/

      - name: Setup Terraform plugin cache
        run:  make set-plugin-cache

      - name: Install and setup Terraform and Terragrunt version
        run:  make tf

      - name: Publish Lambda artifacts
        run:  make publish-lambda-artifacts

      - name: Deployment Plan
        run:  make plan

      - name: Deploy resources
        run:  make apply-ci

      - name: Invalidate Cloudfront cache
        run:  make invalidate-cache
