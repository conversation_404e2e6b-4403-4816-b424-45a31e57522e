import EmailUpdatesContext from "./EmailUpdatesContext";
import { useState } from "react";

const EmailUpdatesProvider = (props) => {

    const [emailAddress, setEmailAddress] = useState("");
    const [stayUpToDate, setStayUptoDate] = useState(false);
    const [responseId, setResponseId] = useState("")


    const state = {
        emailAddress,
        responseId,
        stayUpToDate,
        setEmailAddress,
        setStayUptoDate,
        setResponseId
    }

    return (
        <EmailUpdatesContext.Provider value={state}>
            {props.children}
        </EmailUpdatesContext.Provider>
    )
}

export default EmailUpdatesProvider;