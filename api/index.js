/**
 * Submits contact us form details from across the site
 * @param {string} formType type of contact form e.g email only
 * @param {object} formData data entered by user
 * @returns 
 */
export const contactUs = async (formType, formData) => {
    const rawResponse = await fetch(process.env.NEXT_PUBLIC_LAMBDA_HTTP_URL, {
        method: 'POST',
        headers: {
            'Accept': 'application/json',
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({ formType, formData })
    });
    const res = await rawResponse.json();
    return res
}