@import url("https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;600&display=swap");

/********* Variables *********/

:root {
  --primary: #48586b;
  --secondary: #1d6ec5;
  --pink: #ff82bc;
  --gray: #f5f5f5;
  --gray-lte: #e4e4e4;
  --gray-lt: #bfbfbf;
  --gray-dr: #c4c4c4;
  --green: #3db06e;
  --red: #eb5a2b;
}

/********* Common Style *********/

* {
  -moz-box-sizing: border-box;
  -webkit-box-sizing: border-box;
  -ms-box-sizing: border-box;
  -o-box-sizing: border-box;
  box-sizing: border-box;
  outline: none;
}

html {
  border: 0;
  margin: 0;
  outline: 0;
  padding: 0;
  vertical-align: baseline;
}

body,
button,
input,
select,
textarea {
  font-family: "Poppins", sans-serif;
  font-weight: 400;
  font-style: normal;
  color: var(--primary);
  font-size: 16px;
  line-height: 1.4;
  margin: 0;
  padding: 0;
}

body {
  background-color: white;
  min-width: 290px;
}

a {
  color: inherit;
  text-decoration: none;
  outline: 0;
  cursor: pointer;

  &:hover {
    text-decoration: none;
    color: var(--secondary);
  }

  &:visited,
  &:active {
    text-decoration: none;
  }
}

img {
  outline: none;
  max-width: 100%;
  height: auto;
  vertical-align: middle;
  font-size: 14px;
}

hr {
  margin: 30px 0;
}

::selection {
  background-color: var(--secondary);
  color: white;
}

a,
.btn {
  -webkit-transition: background-color 0.3s ease;
  -moz-transition: background-color 0.3s ease;
  -ms-transition: background-color 0.3s ease;
  -o-transition: background-color 0.3s ease;
  transition: background-color 0.3s ease;
}

.btn:disabled {
  background: rgb(128 167 209);
}

.m-0 {
  margin: 0;
}

.ml-auto {
  margin-left: auto;
}

.mt-auto {
  margin-top: auto;
}

.mb-15 {
  margin-bottom: 15px;
}

.p-0 {
  padding: 0;
}

.p-8 {
  padding: 8px;
}

.w100 {
  width: 100%;
}

.w70 {
  width: 70%;
}

.w30 {
  width: 31%;
}

.w45 {
  width: 45%;
  min-width: 46%;
}

.w55 {
  width: 55%;
}

.bg {
  color: white;
  padding: 16px;
  position: relative;
}

.gray {
  background-color: var(--gray);
  color: var(--primary);
}

.black {
  background-color: var(--primary);
}

.green {
  background-color: var(--green);
}

.blue {
  background-color: var(--blue);
}

.yellow {
  background-color: var(--yellow);
}

.orange {
  background-color: var(--orange);
}

.pink {
  background-color: var(--secondary);
}

.object-fit {
  width: 100%;
  height: 100%;
  object-fit: cover;
  display: block;
}

.box {
  padding: 8px;
  overflow: hidden;
  text-align: center;
  position: relative;
}

.big-text {
  font-size: 200px;
  font-size: 15vw;
}

.opacity {
  opacity: 0.6;
}

/********* Tags *********/

h1,
h2,
h3,
h4,
h5,
h6 {
  line-height: 1.1;
  margin: 0 0 20px;
  font-weight: 600;
}

h1 {
  font-size: 48px;
}

h2 {
  font-size: 32px;
}

h3 {
  font-size: 18px;
}

h4 {
  font-size: 16px;
}

h5 {
  font-size: 14px;
}

h6 {
  font-size: 12px;
}

p,
table,
fieldset,
address {
  margin: 0 0 20px;
}

/* ******** Grid : 12 Coloum ******** */

.col {
  float: left;
  position: relative;
  padding-left: 8px;
  padding-right: 8px;
}

.col-12 {
  width: 100%;
}

.col-11 {
  width: 91.66666667%;
}

.col-10 {
  width: 83.33333333%;
}

.col-9 {
  width: 75%;
}

.col-8 {
  width: 66.66666667%;
}

.col-7 {
  width: 58.33333333%;
}

.col-6 {
  width: 50%;
}

.col-5 {
  width: 41.66666667%;
}

.col-4 {
  width: 33.33333333%;
}

.col-3 {
  width: 25%;
}

.col-2 {
  width: 16.66666667%;
}

.col-1 {
  width: 8.33%;
}

/********** Alignment **********/

.pull-left {
  float: left !important;
}

.pull-right {
  float: right !important;
}

.text-left {
  text-align: left;
}

.text-right {
  text-align: right;
}

.text-center {
  text-align: center;
}

.text-uppercase {
  text-transform: uppercase;
}

.justify {
  text-align: justify;
}

/********* Clear / Row / Container *********/

.container {

  &:before,
  &:after {
    content: "";
    display: table;
  }
}

.row {

  &:before,
  &:after {
    content: "";
    display: table;
  }
}

.container:after {
  clear: both;
}

.row {
  &:after {
    clear: both;
  }

  margin: 0 -8px;
}

.container {
  margin: 0 auto;
  width: 100%;
  max-width: 1060px;
  min-width: 280px;
  padding-left: 20px;
  padding-right: 20px;
}

.container-fluid {
  width: 100%;
  padding-left: 20px;
  padding-right: 20px;
}

.section {
  padding: 60px 0;
  clear: both;
}

/********** Flex / Position **********/

.relative {
  position: relative;
}

.center-align {
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
  position: absolute;
}

.flex {
  display: flex;
  align-items: center;
}

.flex-reverse {
  display: -webkit-flex;

  /* Safari */

  -webkit-flex-direction: row-reverse;

  /* Safari 6.1+ */

  display: flex;
  flex-direction: row-reverse;
  align-items: center;
}

.flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
}

.flex-center-end {
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

.flex-align-items {
  display: flex;
  align-items: center;
}

.flex-space-between {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.d-none {
  display: none;
}

/********** Button **********/

[type="button"],
[type="reset"],
[type="submit"],
button {
  -webkit-appearance: button;
}

.btn {
  display: inline-block;
  font-size: 14px;
  line-height: 34px;
  text-transform: uppercase;
  padding: 0 20px;
  color: white;
  text-align: center;
  vertical-align: middle;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  background-color: var(--primary);
  border: solid 1px transparent;
  background: rgb(23, 88, 158);
  background: linear-gradient(4deg,
      rgba(23, 88, 158, 1) 0%,
      rgba(29, 110, 197, 1) 100%);
  border-radius: 5px;

  &:focus {
    outline: 0;
    box-shadow: none;
  }

  &:hover {
    text-decoration: none;
    background: rgb(23, 88, 158);
    background: linear-gradient(4deg,
        rgba(29, 110, 197, 1) 0%,
        rgba(23, 88, 158, 1) 100%);
  }
}

.link {
  position: relative;
  background: linear-gradient(to bottom, currentColor 25%, transparent 25%);
  background-repeat: no-repeat;
  background-position: right 1.4em;
  background-size: 0% 0.25em;
  transition: background-size 0.3s;
  display: inline-block;
  color: var(--secondary);
  padding-bottom: 1px;

  &:hover {
    background-image: linear-gradient(to bottom,
        currentColor 25%,
        transparent 25%);
    background-size: 100% 0.25em;
    background-position: left 1.4em;
  }
}

.link-underline {
  position: relative;
  padding-bottom: 1px;

  &::after {
    content: "";
    position: absolute;
    left: 0;
    bottom: 0;
    width: 100%;
    height: 1px;
    background-color: currentColor;
    transition: all 0.3s ease;
  }

  &:hover::after {
    width: 0;
    left: auto;
    right: 0;
  }
}

.link-icon {
  display: inline-block;
  width: 30px;
  height: 26px;
  background: url(/assets/images/icon-arrow.png) no-repeat center center;
  background-size: 100% auto;
  transition: all 0.3s ease;

  &:hover {
    margin-left: 8px;
  }
}

/********** Form **********/

.form-group {
  position: relative;
}

.form {
  width: 100%;
  max-width: 560px;
  margin: 0 auto;
  padding: 20px;

  label {
    font-size: 20px;
    color: var(--secondary);
  }
}

.form-control {
  display: block;
  width: 100%;
  height: 36px;
  padding: 10px 12px;
  font-family: "Poppins", sans-serif;
  font-size: 12px;
  font-weight: 400;
  line-height: 1.5;
  color: var(--primary);
  border-radius: 5px;
  background-color: transparent;
  background-clip: padding-box;
  border: 1px solid var(--gray-lt);
  transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;

  &:focus {
    outline: 0;
  }

  &.error {
    border-color: var(--red);
    outline: 0;
  }
}

.btn-send {
  background: white url(/assets/images/icon-send.svg) no-repeat center center;
  width: 40px;
  height: 100%;
  position: absolute;
  top: 0;
  right: 0;
  border: none;
  background-size: 20px auto;
  cursor: pointer;

  &:hover {
    background-color: var(--gray);
  }
}

input {

  &::-webkit-outer-spin-button,
  &::-webkit-inner-spin-button {
    -webkit-appearance: none;
    margin: 0;
  }

  &[type="number"] {
    -moz-appearance: textfield !important;
  }
}

input[type="checkbox"] {
  display: none;

  + {
    label {
      display: inline-block;
      position: relative;
      padding-left: 30px;
      font-size: 12px;
      cursor: pointer;
      text-align: left;
      min-height: 17px;
      margin-bottom: 6px;
      padding-top: 2px;
      color: #777;
      -webkit-user-select: none;
      -moz-user-select: none;
      -ms-user-select: none;

      &:before {
        content: "";
        display: block;
        width: 17px;
        height: 17px;
        border: 1px solid #d3d3d3;
        position: absolute;
        left: 0;
        top: 0;
        -webkit-transition: all 0.12s, border-color 0.08s;
        transition: all 0.12s, border-color 0.08s;
        border-radius: 3px;
      }

      &:after {
        content: "";
        display: block;
        width: 5px;
        height: 10px;
        position: absolute;
        top: 1px;
        left: 6px;
        border-radius: 0;
        opacity: 0;
        border: 1px solid #d3d3d3;
        -webkit-transition: all 0.12s, border-color 0.08s;
        transition: all 0.12s, border-color 0.08s;
      }
    }
  }

  &:checked {
    + {
      label {
        &::before {
          border-color: var(--secondary);
        }

        &:after {
          opacity: 1;
          border-color: var(--secondary);
          border-top-color: transparent;
          border-left-color: transparent;
          -webkit-transform: rotate(45deg);
          transform: rotate(45deg);
        }
      }
    }
  }
}

.radio-group{
  display: flex;
  align-items: center;
  margin-bottom: 20px;
  .radio{
    margin-right: 12px;
  }
  label{
    margin-right: 12px;
    font-size: 13px;
    color: #777;
  }
}

.radio-group{
  input[type="radio"] {
    display: none;
  
    + {
      label {
        display: inline-block;
        position: relative;
        padding-left: 30px;
        font-size: 12px;
        cursor: pointer;
        text-align: left;
        min-height: 17px;
        margin-bottom: 6px;
        padding-top: 2px;
        color: #777;
        -webkit-user-select: none;
        -moz-user-select: none;
        -ms-user-select: none;
  
        &:before {
          content: "";
          display: block;
          width: 17px;
          height: 17px;
          border: 1px solid #d3d3d3;
          position: absolute;
          left: 0;
          top: 0;
          -webkit-transition: all 0.12s, border-color 0.08s;
          transition: all 0.12s, border-color 0.08s;
          border-radius: 3px;
        }
  
        &:after {
          content: "";
          display: block;
          width: 5px;
          height: 10px;
          position: absolute;
          top: 1px;
          left: 6px;
          border-radius: 0;
          opacity: 0;
          border: 1px solid #d3d3d3;
          -webkit-transition: all 0.12s, border-color 0.08s;
          transition: all 0.12s, border-color 0.08s;
        }
      }
    }
  
    &:checked {
      + {
        label {
          &::before {
            border-color: var(--secondary);
          }
  
          &:after {
            opacity: 1;
            border-color: var(--secondary);
            border-top-color: transparent;
            border-left-color: transparent;
            -webkit-transform: rotate(45deg);
            transform: rotate(45deg);
          }
        }
      }
    }
  }
}

/********* Layout *********/

.main {
  display: block;
}

.carousel {
  position: relative;

  .slick-slide {
    position: relative;
    height: 515px;
    background-color: var(--gray);

    img {
      object-fit: cover;
      height: 100%;
      width: 100%;
    }

    .container {
      position: relative;
      padding-top: 80px;
      z-index: 1;
    }

    .item {
      &:after {
        content: "";
        display: block;
        width: 100%;
        height: 100%;
        position: absolute;
        top: 0;
        left: 0;
        background: rgb(29, 111, 197);
        background: linear-gradient(90deg,
            rgba(29, 111, 197, 1) 0%,
            rgba(29, 111, 197, 0.5046612394957983) 51%,
            rgba(29, 111, 197, 1) 100%);
        opacity: 0.4;
      }
    }

    .carousel-content {
      width: 100%;
      max-width: 730px;
      color: white;
      text-shadow: 2px 1px 3px rgb(0 0 0 / 30%);

      h2 {
        font-size: 64px;
        font-weight: 300;

        b {
          display: block;
        }
      }

      p {
        max-width: 700px;
      }

      .link-icon {
        background: url(/assets/images/icon-arrow-white.png) no-repeat center center;
        background-size: 100% auto;
      }
    }
  }
}

.subscribe-section .form-control {
  max-width: 230px;
  margin: 0 15px 0 25px;
}

.news-section {
  padding: 60px 0;

  h3 {
    font-family: Arial, Helvetica, sans-serif;
  }

  a {
    display: block;
  }
}

.title {
  margin-bottom: 30px;

  h2 {
    font-size: 22px;
    font-weight: 400;
    margin: 0;
  }
}

.news h3 a {
  font-size: 14px;
  color: var(--secondary);
  background-color: var(--gray);
  text-align: center;
  padding: 8px;
}

.two-text-section {
  padding: 50px 0;

  .col {
    &:first-child {
      padding-right: 35px;
    }

    &:last-child {
      padding-left: 35px;
      border-left: solid 1px var(--gray-lte);
    }
  }

  .custom-title {
    margin-bottom: 20px;

    h4 {
      font-size: 18px;
      color: #2170c5;
    }
  }

  p {
    color: #48586b;
    font-size: 16px;
    margin: 0;
  }
}

.zigzag-section {
  padding: 30px 0 0;

  .col img {
    height: 280px;
    width: 280px;
    object-fit: cover;
  }

  h3 {
    color: #1d6ec5;
    font-size: 22px;
    line-height: 1.3;
    font-weight: 400;
  }

  .col {
    &:first-child {
      padding-right: 25px;
    }

    &:last-child {
      padding-left: 25px;
    }
  }

  .item-link {
    img {
      width: 30px;
      height: 30px;
      object-fit: contain;
      transition-duration: 0.3s;
    }

    &:hover img {
      transform: translateX(10px);
    }
  }

  .row {
    display: flex;
    align-items: center;
    margin: 0 0 100px 0;
    position: relative;

    &:last-child {
      margin: 0 0 60px 0;
    }

    &:after {
      content: "";
      width: 100%;
      height: 1px;
      border-bottom: solid 1px var(--gray-lte);
      position: absolute;
      bottom: -49px;
      left: 0;
    }
  }

  .item-link {
    display: inline-block;
    max-width: 30px;
  }
}

.zigzag-width .col img {
  width: 100%;
}

.form-section {
  padding: 70px 0 40px;
  background: url(/assets/images/shadow-bottom.png) no-repeat center top;

  .form-field {
    margin-bottom: 50px;
  }

  .form-text {
    text-align: center;
    margin-bottom: 20px;

    h3 {
      font-size: 22px;
      color: #1d6ec5;
      margin-bottom: 10px;
      font-weight: 500;
    }

    p {
      color: #48586b;
      margin-bottom: 10px;
    }

    span {
      color: #bfbfbf;
      font-size: 14px;
    }
  }

  .row {
    display: flex;
    flex-direction: row;
    height: 100%;
    align-items: stretch;
  }

  .col {
    &:first-child {
      padding-right: 40px;
    }

    &:last-child {
      padding-left: 40px;
      border-left: solid 1px var(--gray-lte);
      padding-bottom: 70px;
    }
  }

  .form-buttons {
    .btn {
      position: relative;
      left: 0;
      margin-right: 20px;
    }
  }

  h4,
  h5 {
    font-weight: 400;
    line-height: 1.5;
  }

  .form-control {
    margin-bottom: 20px;
    height: auto;
    padding: 12px 16px;
    border: solid 1px #d3d3d3;
    -webkit-appearance: none;
    -moz-appearance: none;

    &::placeholder {
      color: #bfbfbf;
    }

    &:last-child {
      margin-bottom: 0px;
    }
  }

  .two-colum {
    display: grid;
    grid-template-columns: 2fr 1fr;
    grid-gap: 20px;
    align-items: baseline;
  }

  .btn {
    position: absolute;
    bottom: 0;
    left: 40px;
    height: 44px;
    width: 130px;
  }

  option:first {
    color: #999;
  }

  select {
    position: relative;
    background-repeat: no-repeat;
    background-position: 95%;
    background-image: url(/assets/images/triangle-image.png);
  }
}

.form-opportunities {
  .btn {
    position: inherit;
    margin-top: 35px;
    width: auto;
  }
}

.form-email-updates {
  .row {
    justify-content: center;
  }

  .col:last-child {
    border: none;
  }
}

.filter-section {
  padding: 30px 0;

  div.custom-dropdown {
    position: relative;
    background-repeat: no-repeat;
    background-position: right center;
    background-image: url(/assets/images/triangle-image.png);

    >div {
      &.caption {
        padding: 6px 24px 6px 12px;
        border-radius: 3px;
        cursor: pointer;
        color: #1d6ec5;
      }

      &.list {
        position: absolute;
        width: 230px;
        border-radius: 0 0 3px 3px;
        display: none;

        >div.item {
          padding: 11px 24px;
          cursor: pointer;
          width: 100%;
          transition: all 0.3s ease;

          &:hover {
            color: #000;
            background-color: var(--gray);
          }

          &.selected {
            font-weight: bold;
          }
        }
      }
    }

    &.open>div {
      &.caption {
        border-radius: 3px 3px 0 0;
      }

      &.list {
        padding: 10px 0;
        display: block;
        background: #fff;
        border-radius: 10px 10px 10px 10px;
        z-index: 11;
        box-shadow: 0 8px 14px -6px #0000004f;
        overflow: hidden;
      }
    }
  }

  .left-part {
    display: flex;
    align-items: center;
  }

  h2 {
    padding-right: 20px;
    border-right: solid 1px var(--gray-lte);
    margin-bottom: 0;
    font-size: 16px;
    font-weight: 400;
  }

  .custom-dropdown {
    font-weight: 700;
    margin-left: 10px;

    &.open {
      background-color: #fff;
      width: auto;
      border-bottom: 0px;
      border-radius: 10px 10px 0 0;
    }
  }

  .search-form {
    text-align: right;
    justify-content: right;

    .form-control {
      width: 220px;
      margin-right: 10px;
    }
  }
}

.updates-section {
  padding: 0;


  .news {
    margin-bottom: 40px;

    h3 {
      font-family: Arial, Helvetica, sans-serif;
    }

  
  }

  &.events-section{
    .news-img{
      height: auto;
    }
  }
}

.contact-form {
  padding-bottom: 70px;
  background: none;

  .row {
    justify-content: center;
  }

  .col:last-child {
    border: none;
  }
}

.errorText {
  color: #d8000c;
  background-color: #ffbaba;
  border-radius: 3px;
  margin-top: -12px;
  margin-bottom: 20px;
  padding: 8px 10px;
  font-size: 12px;
}

.contact-form {
  background: url(/assets/images/shadow-bottom.png) no-repeat center top;
  padding-top: 60px;
}

.contact-info {
  padding-bottom: 70px;

  h2 {
    font-size: 22px;
  }

  h3 {
    font-size: 16px;
    color: var(--secondary);
  }
}

.blog-banner {
  padding: 55px 10px;
  position: relative;
  display: flex;
  align-items: center;
  min-height: 200px;
  text-shadow: 2px 1px 3px rgb(0 0 0 / 30%);

  img {
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    object-fit: cover;
  }

  h1 {
    font-size: 40px;
    font-weight: 300;
    max-width: 650px;
    margin: auto;
    z-index: 1;
    position: relative;
    color: #fff;
    text-align: center;
  }

  &:after {
    content: "";
    display: block;
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0;
    left: 0;
    background: rgb(29, 111, 197);
    opacity: 0.4;
  }
}

.blog-section {
  padding: 0 0 50px 0;

  .author {
    min-width: 250px;
    margin-right: 30px;
    position: sticky;
    top: 50px;
    height: 100%;

    h3 {
      margin: 0 0 10px;
    }

    p {
      margin: 0 0 5px;
    }
  }

  .blog-details {

    h2,
    h3 {
      font-weight: 400;
      color: #1d6ec5;
      line-height: 1.3;
      margin: 0 0 15px;
    }

    h4 {
      font-weight: 400;
      font-style: italic;
    }

    // p {
    //   margin: 0 0 30px;
    // }
  }
}

.blog-details-banner {
  overflow: hidden;
  position: relative;
  margin-bottom: 60px;

  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
}

.blog-carousel {
  position: relative;
  margin-bottom: 60px;

  .slick-slider {
    padding: 0;
    height: 300px;

    div {
      height: 100%;
      display: block !important;
    }

    .slick-slide {
      height: 100%;
    }

    img {
      width: 100%;
      height: 100% !important;
    }
  }

  // &.blog-carousel-img {
  //   .slick-slider {
  //     height: 500px;
  //   }
  // }
}

.category-date {
  width: 100%;
  padding: 10px 0;
  background-color: var(--secondary);
  color: white;

  a {
    font-weight: 500;
    margin-right: auto;

    &:hover {
      color: white;
    }
  }

  span {
    font-size: 14px;
  }
}

.blog-content {
  h2 {
    margin-bottom: 30px;
    max-width: 700px;
    font-weight: 400;
    line-height: 1.3;
  }

  h3 {
    margin-bottom: 30px;
  }

  // p {
  //   margin-bottom: 40px;
  // }
}

.blog-quotes {
  margin-bottom: 60px;

  h4 {
    max-width: 700px;
    font-size: 20px;
    font-weight: 300;
    font-style: italic;
    color: var(--secondary);
    line-height: 1.4;
    margin: 0;
    border-left: solid 1px var(--gray-lt);
    padding: 15px 20px 15px 30px;
    margin-left: 30px;
  }
}

.blog-img {
  margin-bottom: 60px;
}

.blog-video {
  margin-bottom: 60px;
  text-align: center;

  video {
    height: auto;
    max-width: 100%;
  }
}

.blog-col-quotes {
  .col {
    &:first-child {
      padding-right: 0;
    }

    &:last-child {
      border-left: solid 1px var(--gray-lt);
      padding-left: 25px;
      min-width: calc(50% + 20px);
    }
  }

  h4 {
    font-size: 20px;
    font-weight: 300;
    font-style: italic;
    color: var(--secondary);
    line-height: 1.4;
    border-right: solid 7px var(--secondary);
    padding-right: 40px;
  }
}

.blog-social {
  justify-content: center;
  padding: 60px 0;

  .bookmark {
    margin-right: 50px;
  }

  .print {
    margin-right: 50px;
  }

  img {
    height: 18px;
    opacity: 0.4;
    transition: all 0.3s ease;
  }

  a {
    &:hover {
      img {
        opacity: 0.8;
      }
    }
  }
}

.social {
  border: solid 1px var(--gray-lte);
  overflow: hidden;
  border-radius: 8px;
  display: flex;
  align-items: center;

  span {
    background-color: var(--gray-lte);
    padding: 8px 16px;
    margin-right: 18px;
  }

  a {
    padding: 8px;
    margin-right: 18px;
  }
}

.blog-buttons {
  padding-bottom: 30px;

  a {
    font-size: 12px;
    font-weight: 600;
    color: var(--gray-dr);
    display: flex;
    align-items: center;
    transition: all 0.3s ease;

    &:hover {
      color: var(--secondary);
    }
  }

  img {
    height: 20px;
    opacity: 0.4;
    transition: all 0.3s ease;
    margin-right: 10px;
  }

  .prev {
    margin-right: auto;
  }

  .next {
    img {
      margin: 0 0 0 10px;
      transform: rotate(180deg);
    }
  }
}

.page-404 {
  text-align: center;
  padding: 40px 0;
}
.img-404 {
  margin: 0 auto 30px;
}

/* Media query ****************************/
@media screen and (min-width: 992px) {
  .carousel-section {
    .slick-slide {

      // height: calc(100vh - 156px);
      .container {
        padding-top: 40px;
      }

      >div {
        display: flex !important;
        align-items: center;
        height: 100%;
      }
    }
  }
}

@media screen and (min-width: 768px) {
  .mobile {
    display: none !important;
  }
}

@media (max-width: 991px) {
  .carousel-content h2 {
    font-size: 40px;
    max-width: 500px;

    p {
      font-size: 40px;
    }
  }

  .zigzag-longtext {
    padding: 50px 0;
  }

  .filter-section {
    div.custom-dropdown>div.caption {
      padding: 11px 14px;
    }
  }
}

@media screen and (max-width: 767px) {

  body,
  button,
  input,
  select,
  textarea {
    font-size: 14px;
  }

  h1,
  h2,
  h3,
  h4,
  h5,
  h6 {
    margin-bottom: 15px;
  }

  .col {
    width: 100%;
  }

  .join {
    .col-8 {
      width: 66.66666667%;
    }

    .col-4 {
      width: 33.33333333%;
    }
  }

  .flex {
    flex-wrap: wrap;
  }

  .desktop {
    display: none !important;
  }

  .scroll {
    overflow: auto;
  }

  .carousel-section {
    .slick-slide {
      height: 410px;
      color: white;
    }

    .carousel-content {
      b {
        color: white;
      }

      // p {
      //   font-size: 14px;
      // }

      .link-icon {
        background-image: url(/assets/images/icon-arrow-white.png);
      }
    }
  }

  .news-section .col h3 {
    margin-bottom: 10px;
  }

  .news {
    min-width: 920px;
    display: flex;
    margin-bottom: 10px;
    .col {
      max-width: 230px;
    }
  }

  .subscribe-section {
    .form {
      padding: 20px 0;
      justify-content: center;
    }

    .form-control {
      margin: 10px 15px 10px 0;
      max-width: 200px;
    }

    .form label {
      margin: 0 15px;
    }
  }

  .carousel-section {
    .carousel-content h2 {
      font-size: 44px;

      b {
        color: white;
      }

      p {
        font-size: 44px;
      }
    }
  }

  .zigzag-section {
    .row {
      display: flex;
      flex-direction: column;

      &:after {
        bottom: -12px;
      }
    }

    .col {
      padding: 0;

      &:first-child {
        padding: 0;
      }

      &:last-child {
        margin-top: 20px;
        padding: 0;
      }

      img {
        width: 100%;
        height: auto;
      }
    }

    .row {
      &:nth-child(even) {
        flex-direction: column-reverse;

        .col {
          &:last-child {
            margin-top: 0;
          }

          &:first-child {
            margin-top: 20px;
          }
        }
      }

      &:last-child {
        margin: 0;
      }

      &:not(:last-child) {
        margin: 0 0 50px 0;
      }
    }

    .item-link {
      max-width: 26px;
      margin-bottom: 20px;
    }
  }

  .filter-section {
    .row {
      display: flex;
      flex-direction: column;
    }

    .left-part h2 {
      font-size: 14px;
      padding-right: 10px;
    }

    div.custom-dropdown {

      >div.caption {
        padding: 5px 24px 5px 0;
        font-size: 14px;
      }
    }

    .search-form {
      display: grid;
      grid-template-columns: 2fr 1fr;
      width: 100%;
      grid-gap: 10px;
      margin-top: 10px;

      .form-control {
        width: 100%;
      }
    }
  }

  .two-text-section {
    padding: 20px 0;

    .col {
      padding: 30px 10px;

      &:first-child {
        padding-right: 10px;
      }

      &:last-child {
        padding: 30px 10px;
        border: none;
        border-top: solid 1px var(--gray-lte);
      }
    }
  }

  .form-section {
    padding: 60px 0 30px;
    background-size: 130%;

    .row {
      flex-direction: column;
    }

    .col {
      &:first-child {
        padding-right: 10px;
      }

      &:last-child {
        margin-top: 30px;
        padding-left: 8px;
        border-left: none;
      }

      .btn {
        left: 10px;
      }

      .form-field {
        margin-bottom: 24px;
      }

      .form-control {
        margin-bottom: 20px;
      }

      &:last-child {
        margin-top: 0;
      }
    }
  }

  .contact-form {
    background-size: 130% auto;
    padding: 50px 0;
  }

  .contact-info {
    padding-bottom: 50px;
  }

  .news-section {
    padding: 50px 0 30px;
  }

  .updates-section {
    padding: 10px 0 0 0;

    .news {
      flex-wrap: wrap;
      min-width: auto;
      margin-bottom: 0;
    }

    &.events-section{
      .news-img{
        height: auto;
      }
    }

    .col {
      max-width: 50%;
      margin-bottom: 30px;
    }
  }

  .blog-banner {
    padding: 55px 10px;

    h1 {
      font-size: 26px;
    }
  }

  .blog-carousel {
    margin: 0 0 30px;

    .slick-slider {
      height: 200px;
    }

    &.blog-carousel-img {
      .slick-slider {
        height: 200px;
      }
    }
  }

  .blog-section {
    padding: 0 0 30px 0;

    h1 {
      font-size: 30px;
    }

    .container {
      flex-wrap: wrap;
    }

    .author {
      min-width: auto;
      position: static;
      width: 100%;
      margin: 0;
      padding: 30px 0 0 0;
      border-top: solid 1px var(--gray-lte);
    }

    .blog-details {
      margin-left: 0;

      h2 {
        font-size: 26px;
      }
    }
  }

  .blog-details-banner {
    height: 200px;
    margin-bottom: 40px;
  }

  .category-date {
    padding: 7px 0;
    font-size: 14px;
  }

  .blog-content {
    h2 {
      font-size: 24px;
      margin-bottom: 20px;
    }

    h3 {
      margin-bottom: 20px;
    }

    // p {
    //   margin-bottom: 30px;
    // }
  }

  .blog-img {
    margin-bottom: 30px;
  }

  .blog-video {
    margin-bottom: 30px;
    text-align: center;

    video {
      height: auto;
      max-width: 100%;
    }
  }

  .quotes-icon {
    min-width: 30px;
  }

  .blog-quotes {
    flex-wrap: nowrap;
    margin-bottom: 30px;

    h4 {
      font-size: 18px;
      padding: 15px 0 15px 20px;
      margin-left: 20px;
    }
  }

  .blog-social {
    flex-wrap: nowrap;
    margin-bottom: 30px;
    padding: 30px 0 0 0;

    .bookmark {
      margin-right: 24px;
    }

    .print {
      margin-right: 24px;
    }
  }

  .blog-col-quotes {
    h4 {
      font-size: 18px;
      padding: 0 0 0 20px;
      margin-bottom: 30px;
      border: none;
      border-left: solid 7px var(--secondary);
    }

    .col {
      &:last-child {
        border: none;
        padding: 0 8px;
      }
    }
  }

  .social {
    span {
      padding: 7px 10px;
      margin-right: 7px;
    }

    a {
      padding: 7px;
      margin-right: 7px;
    }
  }
}

@media print {
  .noprint {
    display: none;
  }
}

.event-section {
  .flex {
    flex-wrap: nowrap;
  }

  .blog-social {
    margin: 5px 0 0 -8px;
    padding: 0;
    justify-content: flex-start;

    .print {
      display: none;
    }

    .social {
      border: none;
      border-radius: 0;

      a {
        margin-right: 0;
        padding: 0;
        width: 34px;
        height: 34px;
        display: flex;
        align-items: center;
        justify-content: center;
      }

      span {
        display: none;
      }
    }


    img {
      max-height: 14px;
    }
  }

  .event-card {
    .blog-social {
      position: absolute;
      left: 15px;
      top: 15px;
      margin: 0;

      .social {
        a {
          background-color: rgb(255 255 255 / 60%);
          margin-right: 8px;

          @media screen and (max-width: 767px) {
            width: 30px;
            height: 30px;
          }

          &:hover {
            background-color: #fff;
          }
        }
      }
    }
  }
}