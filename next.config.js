/** @type {import('next').NextConfig} */

const isProd = process.env.NODE_ENV === 'production'

const nextConfig = {
  // webpack: (config, { isServer }) => {
  //   if (isServer) {
  //     require("./utils/sitemap.js");
  //   }
  //   return config;
  // },
  reactStrictMode: true,
  swcMinify: true,
  images: {
    loader: "akamai",
    path: "",
  },
  basePath: "",
  assetPrefix: "",
  trailingSlash: true,
  headers: async () => {
    return [
      {
        source: "/assets/(.*)",
        headers: [
          {
            key: 'cache-control',
            value: 'public, max-age=31536000',
          }
        ],
      },
    ];
  },
  output:'standalone',
  compress: false
};

module.exports = nextConfig;
