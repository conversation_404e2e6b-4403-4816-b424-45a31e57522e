const fetch = require("node-fetch");
class ContentfulApi {
  static async callContentful(query) {
    // const fetchUrl = `https://graphql.contentful.com/content/v1/spaces/${process.env.CONTENTFUL_SPACE_ID}`;
    const fetchUrl = `https://graphql.contentful.com/content/v1/spaces/${process.env.CONTENTFUL_SPACE_ID}/environments/${process.env.CONTENTFUL_ENVIRONMENT}`;

    const fetchOptions = {
      method: "POST",
      headers: {
        Authorization: `Bearer ${process.env.CONTENTFUL_ACCESS_TOKEN}`,
        "Content-Type": "application/json",
      },
      body: JSON.stringify({ query }),
    };

    try {
      const data = await fetch(fetchUrl, fetchOptions).then((response) => response.json());
      return data;
    } catch (error) {
      console.log(error);
      throw new Error("Could not fetch data from Contentful!");
    }
  }

  // Get Featured News For EmailTemplate
  static async GetFeaturedNewsForEmailTemplates() {
    const query = `{
        emailTemplateFeaturedNewsCollection {
          items {
            featuredNewsCollection {
              items {
                ... on Blog {
                  title
                  tags
                  blogImage {
                    url
                  }
                  slug
                }
                ... on BlogWithQuotesAndImages {
                  title
                  tags
                  blogImage {
                    url
                  }
                  slug
                }
              }
            }
          }
        }
      }`;

    const response = await this.callContentful(query);
    const { emailTemplateFeaturedNewsCollection } = response.data;

    let collection =
      emailTemplateFeaturedNewsCollection?.items && emailTemplateFeaturedNewsCollection?.items?.length > 0
        ? emailTemplateFeaturedNewsCollection.items[0].featuredNewsCollection?.items
        : { total: 0, items: [] };

    collection = collection
      .filter((m) => m != null)
      .map((item) => {
        return {
          title: item?.title,
          tags: item?.tags,
          blogImageUrl: item?.blogImage?.url,
          link: `${process.env.SITE_URL}/blog/${item?.slug}`,
        };
      });

    return collection;
  }

  // Get Events News For EmailTemplate
  static async GetFeaturedEventsForEmailTemplates() {
    const query = `{
        emailTemplateEventsCollection {
          items {
            eventsCollection {
              items {
                title
                date
                eventLink
                address
              }
            }
          }
        }
      }`;

    const response = await this.callContentful(query);

    const { emailTemplateEventsCollection } = response.data;

    let collection =
      emailTemplateEventsCollection?.items && emailTemplateEventsCollection?.items?.length > 0
        ? emailTemplateEventsCollection.items[0].eventsCollection?.items
        : { total: 0, items: [] };

    collection = collection.filter((m) => m != null);

    return collection;
  }

  // Get Latest News For EmailTemplate
  static async GetLatestNewsForEmailTemplates() {
    const query = `{
        blogCollection(order: sys_firstPublishedAt_DESC) {
          items {
            title
            tags
            blogImage {
              url
            }
            slug
          }
        }
        blogWithQuotesAndImagesCollection(order: sys_firstPublishedAt_DESC) {
          items {
            title
            tags
            blogImage {
              url
            }
            slug
          }
        }
      }`;

    const response = await this.callContentful(query);

    const { blogCollection, blogWithQuotesAndImagesCollection } = response.data;

    let allBlogs = [...blogCollection?.items, ...blogWithQuotesAndImagesCollection?.items];
    allBlogs = allBlogs.sort(function (a, b) {
      return new Date(b.publishedAt) - new Date(a.publishedAt);
    });

    // allBlogs = allBlogs.filter((m) => m != null).slice(0, 3);

    allBlogs = allBlogs
      .filter((m) => m != null)
      .map((item) => {
        return {
          title: item?.title,
          tags: item?.tags,
          blogImageUrl: item?.blogImage?.url,
          link: `${process.env.SITE_URL}/blog/${item?.slug}`,
        };
      })
      .slice(0, 3);

    return allBlogs;
  }

  // Get Events
  static async GetLatestEventsForEmailTemplates() {
    var date = new Date().toISOString();
    const query = `{
      eventsCollection(
        where: {date_gte: "${date}"}
        order: date_ASC
        limit: 3
      ) {
        items {
          title
          date
          eventLink
          address
        }
      }
    }`;

    const response = await this.callContentful(query);
    let events = response.data?.eventsCollection ? response.data?.eventsCollection?.items : { total: 0, items: [] };
    const dateOptions = {
      weekday: "long",
      year: "numeric",
      month: "long",
      day: "numeric",
    };
    events = events
      .filter((m) => m != null)
      .slice(0, 3)
      .map((item) => {
        return {
          title: item?.title || "",
          date: new Date(item.date).toLocaleString("en-US", dateOptions),
          eventLink: item?.eventLink || "",
          address: item?.address || "",
        };
      });

    return events;
  }
}

module.exports = ContentfulApi;
