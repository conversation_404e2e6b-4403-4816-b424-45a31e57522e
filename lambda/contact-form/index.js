const uuid = require("uuid");
const { putRow } = require("../dynamo");
const { DYNAMO_TABLES, FORM_TYPES, JFKT8_EMAIL_TEMPLATES } = require("../constants");
const { addMember } = require("../mailchimp");
const ContentfulApi = require("../contentful/contentfulApi");
const { sendTemplate } = require("../mailchimp/templates");
const { sendContactFormEmail, sendBusinessOpportunitiesFormEmail, sendEmploymentOpportunitiesFormEmail, sendMyT8FormEmail } = require("../csv-exporter/ses");

exports.handler = async (event) => {
  let response;
  let statusCode;
  let featuredNews;
  let upcomingEvents;
  let vars;
  const headers = {
    "Access-Control-Allow-Headers": "*",
    "Access-Control-Allow-Methods": "GET, POST, OPTIONS", // Allow only GET request
  };

  if (process.env.NODE_ENV === "local") {
    headers["Access-Control-Allow-Origin"] = "*";
  }

  if (event.httpMethod === "OPTIONS") {
    return {
      statusCode: 200,
      headers,
    };
  }

  if (!event.body) {
    return {
      statusCode: 400,
      body: JSON.stringify({
        message: "No data provided",
      }),
    };
  }

  const data = JSON.parse(event.body);
  const id = uuid.v4();
  console.log(data.formType);
  try {
    switch (data.formType) {
      case FORM_TYPES.CONTACT_US:
        await putRow(DYNAMO_TABLES.CONTACT_RESPONSE, data.formData);
        await addMember(data.formData, data.formType);
        await sendContactFormEmail(data.formData);
        // featuredNews = await ContentfulApi.GetLatestNewsForEmailTemplates();
        // vars = [
        //   {
        //     name: "featuredNews",
        //     content: featuredNews,
        //   },
        // ];

        // await sendTemplate("The New JFKT8 - Thanks for reaching out", JFKT8_EMAIL_TEMPLATES.GENERAL_CONTACT, data.formData.email, vars);

        statusCode = 200;
        response = {
          message: "Success",
        };
        break;

      case FORM_TYPES.EMAIL_ONLY:
        await putRow(DYNAMO_TABLES.OPPORTUNITY_RESPONSE, {
          ...data.formData,
          responseId: id,
        });
        await addMember(data.formData, data.formType);

        statusCode = 200;
        response = {
          message: "Success",
          responseId: id,
        };
        break;

      case FORM_TYPES.STAY_UP_TO_DATE:
        await putRow(DYNAMO_TABLES.OPPORTUNITY_RESPONSE, {
          ...data.formData,
          formType: data.formType,
        });
        await addMember(data.formData, data.formType);

        statusCode = 200;
        response = {
          message: "Success",
        };
        break;

      case FORM_TYPES.EMPLOYMENT_OPPORTUNITY:
        await putRow(DYNAMO_TABLES.OPPORTUNITY_RESPONSE, { ...data.formData, responseId: id, formType: data.formType });
        await addMember(data.formData, data.formType);
        await sendEmploymentOpportunitiesFormEmail(data.formData);

        // featuredNews = await ContentfulApi.GetLatestNewsForEmailTemplates();
        // vars = [
        //   {
        //     name: "featuredNews",
        //     content: featuredNews,
        //   },
        // ];

        // await sendTemplate("The New JFKT8 - You're on our list", JFKT8_EMAIL_TEMPLATES.EMPLOYMENT_OPPORTUNITIES, data.formData.email, vars);

        statusCode = 200;
        response = {
          message: "Success",
        };
        break;

      case FORM_TYPES.BUSINESS_OPPORTUNITY:
        await putRow(DYNAMO_TABLES.OPPORTUNITY_RESPONSE, { ...data.formData, responseId: id, formType: data.formType });
        await addMember(data.formData, data.formType);
        await sendBusinessOpportunitiesFormEmail(data.formData);

        // featuredNews = await ContentfulApi.GetLatestNewsForEmailTemplates();
        // upcomingEvents = await ContentfulApi.GetLatestEventsForEmailTemplates();
        // if (isBOLeasing(data.formData)) {
        //   vars = [
        //     {
        //       name: "featuredNews",
        //       content: featuredNews,
        //     },
        //   ];

        //   await sendTemplate("The New JFKT8 - Thanks for connecting", JFKT8_EMAIL_TEMPLATES.BUSINESS_OPPORTUNITIES_LEASING, data.formData.email, vars);
        // }

        // if (isBOGeneral(data.formData)) {
        //   vars = [
        //     {
        //       name: "featuredNews",
        //       content: featuredNews,
        //     },
        //     {
        //       name: "upcomingEvents",
        //       content: upcomingEvents,
        //     },
        //   ];

        //   await sendTemplate("The New JFKT8 - You're on our list", JFKT8_EMAIL_TEMPLATES.BUSINESS_OPPORTUNITIES_GENERAL, data.formData.email, vars);
        // }

        console.log(JSON.stringify(data.formData));

        statusCode = 200;
        response = {
          message: "Success",
        };
        break;

      case FORM_TYPES.MY_T8:
        await putRow(DYNAMO_TABLES.OPPORTUNITY_RESPONSE, { ...data.formData, responseId: id, formType: data.formType });
        await addMember(data.formData, data.formType);
        await sendMyT8FormEmail(data.formData);

        // featuredNews = await ContentfulApi.GetLatestNewsForEmailTemplates();
        // vars = [
        //   {
        //     name: "featuredNews",
        //     content: featuredNews,
        //   },
        // ];

        // await sendTemplate("The New JFKT8 - Thanks for connecting", JFKT8_EMAIL_TEMPLATES.MYT8_CONCESSION, data.formData.email, vars);
        
        statusCode = 200;
        response = {
          message: "Success",
        };
        break;
      default:
        statusCode = 400;
        response = {
          message: "Invalid form type provided",
        };
    }

    return {
      statusCode,
      headers,
      body: JSON.stringify(response),
    };
  } catch (error) {
    console.error(error);
    return {
      headers,
      statusCode: 400,
      body: JSON.stringify({
        message: error,
      }),
    };
  }
};

// const isBOLeasing = (data) => {
//   return data["bi/foodAndBeverage"] || data["bi/retailBusinessBrickMortar"] || data["bi/retailBusinessOnline"];
// };

// const isBOGeneral = (data) => {
//   return data["bi/architectureDesignEngineering"] || data["bi/construction"] || data["bi/productManufacturer"] || data["bi/supplier"] || data["bi/other	"];
// };
