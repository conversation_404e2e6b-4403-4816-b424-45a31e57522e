// Load the AWS SDK for Node.js
const AWS = require("aws-sdk");
const SES = new AWS.SES();
const { BUSINESS_INTEREST_OPTIONS, CERTIFICATIONS_OPTIONS, EMPLOYMENT_INTEREST_OPTIONS } = require("../../constants");

const sendEmail = async (data) => {
  console.log(JSON.stringify(data))
  const ccAddresses = process.env.CC_EMAILS;
  const params = {
    Destination: {
      ToAddresses: [data.email],
      CcAddresses: ccAddresses ? ccAddresses.split(",") : [],
    },
    Message: {
      Body: {
        Html: {
          Charset: "UTF-8",
          Data: `<html>
                    <head></head>
                    <body>
                      <p>Dear Partner,</p>
                      <p>Please find the inquiries at <a href=${data.fileLink}>${data.fileLink}</a> </p>
                      <br/>
                      <p>Regards,</p>
                      <p>JFK T8 Team </p>
                    </body>
                    </html>`,
        },
      },
      Subject: {
        Charset: "UTF-8",
        Data: data.subject,
      },
    },
    Source: process.env.SOURCE_EMAIL,
  };

  // Create the promise and SES service object
  const result = await SES.sendEmail(params).promise();

  return result;
};

const sendContactFormEmail = async (data) => {
  let toAddresses = process.env.JFKT8CONNECT_EMAIL;
  // if (data.inquiryType == "Media Inquiry") {
  //   toAddresses = process.env.CONTACT_MEDIA_INQUIRY_EMAIL;
  // }

  const params = {
    Destination: {
      ToAddresses: toAddresses ? toAddresses.split(",") : [],
    },
    Message: {
      Body: {
        Html: {
          Charset: "UTF-8",
          Data: `<html>
          <head></head>
          <body>
          <p style="margin:0in;font-size:12pt;font-family:Calibri,sans-serif">Below are the details for Contact form submission</p>
          <br><br>
          <table border="0" cellspacing="5" cellpadding="0">
              <tbody>
                  <tr>
                  <td valign="top" style="padding: 0.75pt">
                      <div style="margin: 0in; font-size: 11pt; font-family: Calibri, sans-serif">FIRST NAME:<u></u><u></u></div>
                  </td>
                  <td style="padding: 0.75pt">
                      <div style="margin: 0in; font-size: 11pt; font-family: Calibri, sans-serif">${data.firstName}<u></u><u></u></div>
                  </td>
                  </tr>
                  <tr>
                  <td valign="top" style="padding: 0.75pt">
                      <div style="margin: 0in; font-size: 11pt; font-family: Calibri, sans-serif">LAST NAME:<u></u><u></u></div>
                  </td>
                  <td style="padding: 0.75pt">
                      <div style="margin: 0in; font-size: 11pt; font-family: Calibri, sans-serif">${data.lastName}<u></u><u></u></div>
                  </td>
                  </tr>
                  <tr>
                  <td valign="top" style="padding: 0.75pt">
                      <div style="margin: 0in; font-size: 11pt; font-family: Calibri, sans-serif">EMAIL:<u></u><u></u></div>
                  </td>
                  <td style="padding: 0.75pt">
                      <div style="margin: 0in; font-size: 11pt; font-family: Calibri, sans-serif">
                      <a href="mailto:${data.email}" style="color: blue; text-decoration: underline" target="_blank">${data.email}</a><u></u><u></u>
                      </div>
                  </td>
                  </tr>
                  <tr>
                  <td valign="top" style="padding: 0.75pt">
                      <div style="margin: 0in; font-size: 11pt; font-family: Calibri, sans-serif">COMPANY / ORGANIZATION:<u></u><u></u></div>
                  </td>
                  <td style="padding: 0.75pt">
                      <div style="margin: 0in; font-size: 11pt; font-family: Calibri, sans-serif">${data.company}<u></u><u></u></div>
                  </td>
                  </tr>
                  <tr>
                  <td valign="top" style="padding: 0.75pt">
                      <div style="margin: 0in; font-size: 11pt; font-family: Calibri, sans-serif">PHONE:<u></u><u></u></div>
                  </td>
                  <td style="padding: 0.75pt">
                      <div style="margin: 0in; font-size: 11pt; font-family: Calibri, sans-serif">${data.mobile}<u></u><u></u></div>
                  </td>
                  </tr>
                  <tr>
                  <td valign="top" style="padding: 0.75pt">
                      <div style="margin: 0in; font-size: 11pt; font-family: Calibri, sans-serif">YOUR MESSAGE:<u></u><u></u></div>
                  </td>
                  <td style="padding: 0.75pt">
                      <div style="margin: 0in; font-size: 11pt; font-family: Calibri, sans-serif">
                      ${data.message}
                      </div>
                  </td>
                  </tr>
                  <tr>
                    <td valign="top" style="padding: 0.75pt">
                        <div style="margin: 0in; font-size: 11pt; font-family: Calibri, sans-serif">Inquiry Type:<u></u><u></u></div>
                    </td>
                    <td style="padding: 0.75pt">
                        <div style="margin: 0in; font-size: 11pt; font-family: Calibri, sans-serif">${data.inquiryType}<u></u><u></u></div>
                    </td>
                    </tr>
              </tbody>
              </table>
          </body>
          </html>`,
        },
      },
      Subject: {
        Charset: "UTF-8",
        Data: "JFKT8 - Contact Us form submission",
      },
    },
    Source: process.env.SOURCE_EMAIL,
  };

  // Create the promise and SES service object
  const result = await SES.sendEmail(params).promise();

  return result;
};

const sendBusinessOpportunitiesFormEmail = async (data) => {
  var businessInterests = Object.entries(data)
    .filter((x) => x[0].startsWith("bi") && x[1])
    .map((item) => {
      return BUSINESS_INTEREST_OPTIONS.find((x) => x.key == item[0]).value;
    })
    .join(", ");

  var certification = Object.entries(data)
    .filter((x) => x[0].startsWith("cert") && x[1])
    .map((item) => {
      return CERTIFICATIONS_OPTIONS.find((x) => x.key == item[0]).value;
    })
    .join(", ");
  const toAddresses = process.env.FORMS_INTAKE_EMAIL;

  console.log(toAddresses);
  const params = {
    Destination: {
      ToAddresses: toAddresses ? toAddresses.split(",") : [],
    },
    Message: {
      Body: {
        Html: {
          Charset: "UTF-8",
          Data: `<html>
          <head></head>
          <body>
            <p style="margin: 0in; font-size: 12pt; font-family: Calibri, sans-serif">Below are the details for Business Opportunities form submission</p>
            <br /><br />
            <table border="0" cellspacing="5" cellpadding="0">
              <tbody>
                <tr>
                  <td valign="top" style="padding: 0.75pt">
                    <div style="margin: 0in; font-size: 11pt; font-family: Calibri, sans-serif">FIRST NAME:<u></u><u></u></div>
                  </td>
                  <td style="padding: 0.75pt">
                    <div style="margin: 0in; font-size: 11pt; font-family: Calibri, sans-serif">${data.firstName}<u></u><u></u></div>
                  </td>
                </tr>
                <tr>
                  <td valign="top" style="padding: 0.75pt">
                    <div style="margin: 0in; font-size: 11pt; font-family: Calibri, sans-serif">LAST NAME:<u></u><u></u></div>
                  </td>
                  <td style="padding: 0.75pt">
                    <div style="margin: 0in; font-size: 11pt; font-family: Calibri, sans-serif">${data.lastName}<u></u><u></u></div>
                  </td>
                </tr>
                <tr>
                  <td valign="top" style="padding: 0.75pt">
                    <div style="margin: 0in; font-size: 11pt; font-family: Calibri, sans-serif">EMAIL:<u></u><u></u></div>
                  </td>
                  <td style="padding: 0.75pt">
                    <div style="margin: 0in; font-size: 11pt; font-family: Calibri, sans-serif">
                      <a href="mailto:${data.email}" style="color: blue; text-decoration: underline" target="_blank">${data.email}</a><u></u><u></u>
                    </div>
                  </td>
                </tr>
                <tr>
                  <td valign="top" style="padding: 0.75pt">
                    <div style="margin: 0in; font-size: 11pt; font-family: Calibri, sans-serif">COMPANY / ORGANIZATION:<u></u><u></u></div>
                  </td>
                  <td style="padding: 0.75pt">
                    <div style="margin: 0in; font-size: 11pt; font-family: Calibri, sans-serif">${data.companyName}<u></u><u></u></div>
                  </td>
                </tr>
                <tr>
                  <td valign="top" style="padding: 0.75pt">
                    <div style="margin: 0in; font-size: 11pt; font-family: Calibri, sans-serif">PHONE:<u></u><u></u></div>
                  </td>
                  <td style="padding: 0.75pt">
                    <div style="margin: 0in; font-size: 11pt; font-family: Calibri, sans-serif">${data.mobile}<u></u><u></u></div>
                  </td>
                </tr>
                <tr>
                  <td valign="top" style="padding: 0.75pt">
                    <div style="margin: 0in; font-size: 11pt; font-family: Calibri, sans-serif">Business Address 1:<u></u><u></u></div>
                  </td>
                  <td style="padding: 0.75pt">
                    <div style="margin: 0in; font-size: 11pt; font-family: Calibri, sans-serif">${data.businessAddress1}</div>
                  </td>
                </tr>
                <tr>
                  <td valign="top" style="padding: 0.75pt">
                    <div style="margin: 0in; font-size: 11pt; font-family: Calibri, sans-serif">Business Address 2:<u></u><u></u></div>
                  </td>
                  <td style="padding: 0.75pt">
                    <div style="margin: 0in; font-size: 11pt; font-family: Calibri, sans-serif">${data.businessAddress2}</div>
                  </td>
                </tr>
                <tr>
                  <td valign="top" style="padding: 0.75pt">
                    <div style="margin: 0in; font-size: 11pt; font-family: Calibri, sans-serif">City:<u></u><u></u></div>
                  </td>
                  <td style="padding: 0.75pt">
                    <div style="margin: 0in; font-size: 11pt; font-family: Calibri, sans-serif">${data.city}</div>
                  </td>
                </tr>
                <tr>
                  <td valign="top" style="padding: 0.75pt">
                    <div style="margin: 0in; font-size: 11pt; font-family: Calibri, sans-serif">State:<u></u><u></u></div>
                  </td>
                  <td style="padding: 0.75pt">
                    <div style="margin: 0in; font-size: 11pt; font-family: Calibri, sans-serif">${data.state}</div>
                  </td>
                </tr>
                <tr>
                  <td valign="top" style="padding: 0.75pt">
                    <div style="margin: 0in; font-size: 11pt; font-family: Calibri, sans-serif">Zip Code:<u></u><u></u></div>
                  </td>
                  <td style="padding: 0.75pt">
                    <div style="margin: 0in; font-size: 11pt; font-family: Calibri, sans-serif">${data.zipCode}</div>
                  </td>
                </tr>
                <tr>
                  <td valign="top" style="padding: 0.75pt">
                    <div style="margin: 0in; font-size: 11pt; font-family: Calibri, sans-serif">Website:<u></u><u></u></div>
                  </td>
                  <td style="padding: 0.75pt">
                    <div style="margin: 0in; font-size: 11pt; font-family: Calibri, sans-serif">${data.website}</div>
                  </td>
                </tr>
                <tr>
                  <td valign="top" style="padding: 0.75pt">
                    <div style="margin: 0in; font-size: 11pt; font-family: Calibri, sans-serif">Years in Business:<u></u><u></u></div>
                  </td>
                  <td style="padding: 0.75pt">
                    <div style="margin: 0in; font-size: 11pt; font-family: Calibri, sans-serif">${data.yearsInBusiness}</div>
                  </td>
                </tr>
                <tr>
                  <td valign="top" style="padding: 0.75pt">
                    <div style="margin: 0in; font-size: 11pt; font-family: Calibri, sans-serif">I AM INTERESTED IN BUSINESS OPPORTUNITIES IN :<u></u><u></u></div>
                  </td>
                  <td style="padding: 0.75pt">
                    <div style="margin: 0in; font-size: 11pt; font-family: Calibri, sans-serif">${businessInterests}<u></u><u></u></div>
                  </td>
                </tr>
                <tr>
                  <td valign="top" style="padding: 0.75pt">
                    <div style="margin: 0in; font-size: 11pt; font-family: Calibri, sans-serif">IS YOUR BUSINESS CURRENTLY CERTIFIED IN ANY OF THE FOLLOWING DESIGNATIONS?<u></u><u></u></div>
                  </td>
                  <td style="padding: 0.75pt">
                    <div style="margin: 0in; font-size: 11pt; font-family: Calibri, sans-serif">${certification}<u></u><u></u></div>
                  </td>
                </tr>
                <tr>
                  <td valign="top" style="padding: 0.75pt">
                    <div style="margin: 0in; font-size: 11pt; font-family: Calibri, sans-serif">BRIEFLY DESCRIBE YOUR BUSINESS :<u></u><u></u></div>
                  </td>
                  <td style="padding: 0.75pt">
                    <div style="margin: 0in; font-size: 11pt; font-family: Calibri, sans-serif">${data.aboutBusiness}<u></u><u></u></div>
                  </td>
                </tr>
                <tr>
                  <td valign="top" style="padding: 0.75pt">
                    <div style="margin: 0in; font-size: 11pt; font-family: Calibri, sans-serif">AIRPORT EXPERIENCE :<u></u><u></u></div>
                  </td>
                  <td style="padding: 0.75pt">
                    <div style="margin: 0in; font-size: 11pt; font-family: Calibri, sans-serif">${data.airportExperience}<u></u><u></u></div>
                  </td>
                </tr>
                <tr>
                  <td valign="top" style="padding: 0.75pt">
                    <div style="margin: 0in; font-size: 11pt; font-family: Calibri, sans-serif">EMAIL UPDATES</div>
                  </td>
                  <td style="padding: 0.75pt">
                    <div style="margin: 0in; font-size: 11pt; font-family: Calibri, sans-serif">${data.emailUpdates ? "Yes" : "No"}<u></u><u></u></div>
                  </td>
                </tr>
                <tr>
                  <td valign="top" style="padding: 0.75pt">
                    <div style="margin: 0in; font-size: 11pt; font-family: Calibri, sans-serif">TEXT UPDATES</div>
                  </td>
                  <td style="padding: 0.75pt">
                    <div style="margin: 0in; font-size: 11pt; font-family: Calibri, sans-serif">${data.textUpdates ? "Yes" : "No"}<u></u><u></u></div>
                  </td>
                </tr>
                <tr>
                  <td valign="top" style="padding: 0.75pt">
                    <div style="margin: 0in; font-size: 11pt; font-family: Calibri, sans-serif">ADVANCE NETWORK NEWS</div>
                  </td>
                  <td style="padding: 0.75pt">
                    <div style="margin: 0in; font-size: 11pt; font-family: Calibri, sans-serif">${data.advancedNetworkNews ? "Yes" : "No"}<u></u><u></u></div>
                  </td>
                </tr>
              </tbody>
            </table>
          </body>
        </html>
        `,
        },
      },
      Subject: {
        Charset: "UTF-8",
        Data: "JFKT8 - Business Opportunities form Submission",
      },
    },
    Source: process.env.SOURCE_EMAIL,
  };

  // Create the promise and SES service object
  const result = await SES.sendEmail(params).promise();

  return result;
};

const sendEmploymentOpportunitiesFormEmail = async (data) => {
  var employmentOpportunities = Object.entries(data)
    .filter((x) => x[0].startsWith("ei") && x[1])
    .map((item) => {
      return EMPLOYMENT_INTEREST_OPTIONS.find((x) => x.key == item[0]).value;
    })
    .join(", ");

  const toAddresses = process.env.JFKT8CONNECT_EMAIL;
  const ccAddresses = process.env.EMPLOYMENT_INTAKE_EMAIL;

  const params = {
    Destination: {
      ToAddresses: toAddresses ? toAddresses.split(",") : [],
      CcAddresses: ccAddresses ? ccAddresses.split(",") : [],
    },
    Message: {
      Body: {
        Html: {
          Charset: "UTF-8",
          Data: `<html>
          <head></head>
          <body>
            <p style="margin: 0in; font-size: 12pt; font-family: Calibri, sans-serif">Below are the details for Employment Opportunities form submission</p>
            <br /><br />
            <table border="0" cellspacing="5" cellpadding="0">
              <tbody>
                <tr>
                  <td valign="top" style="padding: 0.75pt">
                    <div style="margin: 0in; font-size: 11pt; font-family: Calibri, sans-serif">FIRST NAME:<u></u><u></u></div>
                  </td>
                  <td style="padding: 0.75pt">
                    <div style="margin: 0in; font-size: 11pt; font-family: Calibri, sans-serif">${data.firstName}<u></u><u></u></div>
                  </td>
                </tr>
                <tr>
                  <td valign="top" style="padding: 0.75pt">
                    <div style="margin: 0in; font-size: 11pt; font-family: Calibri, sans-serif">LAST NAME:<u></u><u></u></div>
                  </td>
                  <td style="padding: 0.75pt">
                    <div style="margin: 0in; font-size: 11pt; font-family: Calibri, sans-serif">${data.lastName}<u></u><u></u></div>
                  </td>
                </tr>
                <tr>
                  <td valign="top" style="padding: 0.75pt">
                    <div style="margin: 0in; font-size: 11pt; font-family: Calibri, sans-serif">EMAIL:<u></u><u></u></div>
                  </td>
                  <td style="padding: 0.75pt">
                    <div style="margin: 0in; font-size: 11pt; font-family: Calibri, sans-serif">
                      <a href="mailto:${data.email}" style="color: blue; text-decoration: underline" target="_blank">${data.email}</a><u></u><u></u>
                    </div>
                  </td>
                </tr>
                <tr>
                  <td valign="top" style="padding: 0.75pt">
                    <div style="margin: 0in; font-size: 11pt; font-family: Calibri, sans-serif">PHONE:<u></u><u></u></div>
                  </td>
                  <td style="padding: 0.75pt">
                    <div style="margin: 0in; font-size: 11pt; font-family: Calibri, sans-serif">${data.mobile}<u></u><u></u></div>
                  </td>
                </tr>
                <tr>
                  <td valign="top" style="padding: 0.75pt">
                    <div style="margin: 0in; font-size: 11pt; font-family: Calibri, sans-serif">Home Address 1:<u></u><u></u></div>
                  </td>
                  <td style="padding: 0.75pt">
                    <div style="margin: 0in; font-size: 11pt; font-family: Calibri, sans-serif">${data.homeAddress1}</div>
                  </td>
                </tr>
                <tr>
                  <td valign="top" style="padding: 0.75pt">
                    <div style="margin: 0in; font-size: 11pt; font-family: Calibri, sans-serif">Home Address 2:<u></u><u></u></div>
                  </td>
                  <td style="padding: 0.75pt">
                    <div style="margin: 0in; font-size: 11pt; font-family: Calibri, sans-serif">${data.homeAddress2}</div>
                  </td>
                </tr>
                <tr>
                  <td valign="top" style="padding: 0.75pt">
                    <div style="margin: 0in; font-size: 11pt; font-family: Calibri, sans-serif">City:<u></u><u></u></div>
                  </td>
                  <td style="padding: 0.75pt">
                    <div style="margin: 0in; font-size: 11pt; font-family: Calibri, sans-serif">${data.city}</div>
                  </td>
                </tr>
                <tr>
                  <td valign="top" style="padding: 0.75pt">
                    <div style="margin: 0in; font-size: 11pt; font-family: Calibri, sans-serif">State:<u></u><u></u></div>
                  </td>
                  <td style="padding: 0.75pt">
                    <div style="margin: 0in; font-size: 11pt; font-family: Calibri, sans-serif">${data.state}</div>
                  </td>
                </tr>
                <tr>
                  <td valign="top" style="padding: 0.75pt">
                    <div style="margin: 0in; font-size: 11pt; font-family: Calibri, sans-serif">Zip Code:<u></u><u></u></div>
                  </td>
                  <td style="padding: 0.75pt">
                    <div style="margin: 0in; font-size: 11pt; font-family: Calibri, sans-serif">${data.zipCode}</div>
                  </td>
                </tr>
                <tr>
                  <td valign="top" style="padding: 0.75pt">
                    <div style="margin: 0in; font-size: 11pt; font-family: Calibri, sans-serif">AIRPORT EMPLOYMENT EXPERIENCE:<u></u><u></u></div>
                  </td>
                  <td style="padding: 0.75pt">
                    <div style="margin: 0in; font-size: 11pt; font-family: Calibri, sans-serif">${data.airportEmploymentExperience}</div>
                  </td>
                </tr>
                <tr>
                  <td valign="top" style="padding: 0.75pt">
                    <div style="margin: 0in; font-size: 11pt; font-family: Calibri, sans-serif">I AM INTERESTED IN EMPLOYMENT OPPORTUNITIES IN :<u></u><u></u></div>
                  </td>
                  <td style="padding: 0.75pt">
                    <div style="margin: 0in; font-size: 11pt; font-family: Calibri, sans-serif">${employmentOpportunities}<u></u><u></u></div>
                  </td>
                </tr>
                <tr>
                  <td valign="top" style="padding: 0.75pt">
                    <div style="margin: 0in; font-size: 11pt; font-family: Calibri, sans-serif">EMAIL UPDATES</div>
                  </td>
                  <td style="padding: 0.75pt">
                    <div style="margin: 0in; font-size: 11pt; font-family: Calibri, sans-serif">${data.emailUpdates ? "Yes" : "No"}<u></u><u></u></div>
                  </td>
                </tr>
                <tr>
                  <td valign="top" style="padding: 0.75pt">
                    <div style="margin: 0in; font-size: 11pt; font-family: Calibri, sans-serif">TEXT UPDATES</div>
                  </td>
                  <td style="padding: 0.75pt">
                    <div style="margin: 0in; font-size: 11pt; font-family: Calibri, sans-serif">${data.textUpdates ? "Yes" : "No"}<u></u><u></u></div>
                  </td>
                </tr>
                <tr>
                  <td valign="top" style="padding: 0.75pt">
                    <div style="margin: 0in; font-size: 11pt; font-family: Calibri, sans-serif">ADVANCE NETWORK NEWS</div>
                  </td>
                  <td style="padding: 0.75pt">
                    <div style="margin: 0in; font-size: 11pt; font-family: Calibri, sans-serif">${data.advancedNetworkNews ? "Yes" : "No"}<u></u><u></u></div>
                  </td>
                </tr>
              </tbody>
            </table>
          </body>
        </html>
        `,
        },
      },
      Subject: {
        Charset: "UTF-8",
        Data: "JFKT8 - Employment Opportunities form Submission",
      },
    },
    Source: process.env.SOURCE_EMAIL,
  };

  // Create the promise and SES service object
  const result = await SES.sendEmail(params).promise();

  return result;
};

const sendMyT8FormEmail = async (data) => {
  const toAddresses = process.env.JFKT8CONNECT_EMAIL;

  const params = {
    Destination: {
      ToAddresses: toAddresses ? toAddresses.split(",") : [],
    },
    Message: {
      Body: {
        Html: {
          Charset: "UTF-8",
          Data: `<html>
          <head></head>
          <body>
            <p style="margin: 0in; font-size: 12pt; font-family: Calibri, sans-serif">Below are the details for My T8 form submission</p>
            <br /><br />
            <table border="0" cellspacing="5" cellpadding="0">
              <tbody>
                <tr>
                  <td valign="top" style="padding: 0.75pt">
                    <div style="margin: 0in; font-size: 11pt; font-family: Calibri, sans-serif">FIRST NAME:<u></u><u></u></div>
                  </td>
                  <td style="padding: 0.75pt">
                    <div style="margin: 0in; font-size: 11pt; font-family: Calibri, sans-serif">${data.firstName}<u></u><u></u></div>
                  </td>
                </tr>
                <tr>
                  <td valign="top" style="padding: 0.75pt">
                    <div style="margin: 0in; font-size: 11pt; font-family: Calibri, sans-serif">LAST NAME:<u></u><u></u></div>
                  </td>
                  <td style="padding: 0.75pt">
                    <div style="margin: 0in; font-size: 11pt; font-family: Calibri, sans-serif">${data.lastName}<u></u><u></u></div>
                  </td>
                </tr>
                <tr>
                  <td valign="top" style="padding: 0.75pt">
                    <div style="margin: 0in; font-size: 11pt; font-family: Calibri, sans-serif">EMAIL:<u></u><u></u></div>
                  </td>
                  <td style="padding: 0.75pt">
                    <div style="margin: 0in; font-size: 11pt; font-family: Calibri, sans-serif">
                      <a href="mailto:${data.email}" style="color: blue; text-decoration: underline" target="_blank">${data.email}</a><u></u><u></u>
                    </div>
                  </td>
                </tr>
                <tr>
                  <td valign="top" style="padding: 0.75pt">
                    <div style="margin: 0in; font-size: 11pt; font-family: Calibri, sans-serif">PHONE:<u></u><u></u></div>
                  </td>
                  <td style="padding: 0.75pt">
                    <div style="margin: 0in; font-size: 11pt; font-family: Calibri, sans-serif">${data.mobile}<u></u><u></u></div>
                  </td>
                </tr>
                <tr>
                  <td valign="top" style="padding: 0.75pt">
                    <div style="margin: 0in; font-size: 11pt; font-family: Calibri, sans-serif">Are you currently an active JFK Airport ID Badge Holder?<u></u><u></u></div>
                  </td>
                  <td style="padding: 0.75pt">
                    <div style="margin: 0in; font-size: 11pt; font-family: Calibri, sans-serif">${data.airportBadgeHolder}</div>
                  </td>
                </tr>
                <tr>
                  <td valign="top" style="padding: 0.75pt">
                    <div style="margin: 0in; font-size: 11pt; font-family: Calibri, sans-serif">What type of opportunities are you interested in?<u></u><u></u></div>
                  </td>
                  <td style="padding: 0.75pt">
                    <div style="margin: 0in; font-size: 11pt; font-family: Calibri, sans-serif">${data.interestedInOpportunities}</div>
                  </td>
                </tr>
                <tr>
                  <td valign="top" style="padding: 0.75pt">
                    <div style="margin: 0in; font-size: 11pt; font-family: Calibri, sans-serif">If you are currently working at JFK Airport, what Terminal(s) do you work in?<u></u><u></u></div>
                  </td>
                  <td style="padding: 0.75pt">
                    <div style="margin: 0in; font-size: 11pt; font-family: Calibri, sans-serif">${data.whatTerminalsDoYouWorkIn}</div>
                  </td>
                </tr>
                <tr>
                  <td valign="top" style="padding: 0.75pt">
                    <div style="margin: 0in; font-size: 11pt; font-family: Calibri, sans-serif">If you currently work at JFK Airport in what capacity is your current role (i.e. cook, manager, server, retail, janitorial, etc)?<u></u><u></u></div>
                  </td>
                  <td style="padding: 0.75pt">
                    <div style="margin: 0in; font-size: 11pt; font-family: Calibri, sans-serif">${data.currentRole}</div>
                  </td>
                </tr>
                <tr>
                  <td valign="top" style="padding: 0.75pt">
                    <div style="margin: 0in; font-size: 11pt; font-family: Calibri, sans-serif">Zip Code:<u></u><u></u></div>
                  </td>
                  <td style="padding: 0.75pt">
                    <div style="margin: 0in; font-size: 11pt; font-family: Calibri, sans-serif">${data.zipCode}</div>
                  </td>
                </tr>
                <tr>
                  <td valign="top" style="padding: 0.75pt">
                    <div style="margin: 0in; font-size: 11pt; font-family: Calibri, sans-serif">EMAIL UPDATES</div>
                  </td>
                  <td style="padding: 0.75pt">
                    <div style="margin: 0in; font-size: 11pt; font-family: Calibri, sans-serif">${data.emailUpdates ? "Yes" : "No"}<u></u><u></u></div>
                  </td>
                </tr>
                <tr>
                  <td valign="top" style="padding: 0.75pt">
                    <div style="margin: 0in; font-size: 11pt; font-family: Calibri, sans-serif">TEXT UPDATES</div>
                  </td>
                  <td style="padding: 0.75pt">
                    <div style="margin: 0in; font-size: 11pt; font-family: Calibri, sans-serif">${data.textUpdates ? "Yes" : "No"}<u></u><u></u></div>
                  </td>
                </tr>
                <tr>
                  <td valign="top" style="padding: 0.75pt">
                    <div style="margin: 0in; font-size: 11pt; font-family: Calibri, sans-serif">ADVANCE NETWORK NEWS</div>
                  </td>
                  <td style="padding: 0.75pt">
                    <div style="margin: 0in; font-size: 11pt; font-family: Calibri, sans-serif">${data.advancedNetworkNews ? "Yes" : "No"}<u></u><u></u></div>
                  </td>
                </tr>
              </tbody>
            </table>
          </body>
        </html>
        `,
        },
      },
      Subject: {
        Charset: "UTF-8",
        Data: "JFKT8 - My T8 form Submission",
      },
    },
    Source: process.env.SOURCE_EMAIL,
  };

  // Create the promise and SES service object
  const result = await SES.sendEmail(params).promise();

  return result;
};
module.exports = { sendEmail, sendContactFormEmail, sendBusinessOpportunitiesFormEmail, sendEmploymentOpportunitiesFormEmail, sendMyT8FormEmail };
