const crypto = require("crypto");
const mailchimp = require("@mailchimp/mailchimp_marketing");
const { BUSINESS_INTEREST_OPTIONS, CERTIFICATIONS_OPTIONS, EMPLOYMENT_INTEREST_OPTIONS, STAY_UP_TO_DATE_INTEREST_OPTIONS, FORM_TYPES } = require("../constants");
mailchimp.setConfig({
  apiKey: process.env.MAILCHIMP_API_KEY,
  server: process.env.MAILCHIMP_API_SERVER,
});

// TAGS
const JFK_SUPPLIER_DIVERSITY = "JFK Supplier Diversity";
const FORM_BUSINESS_OPPORTUNITY = "jfkt8form/businessOpportunities";
const FORM_EMPLOYMENT_OPPORTUNITY = "jfkt8form/employmentOpportunities";
const FORM_CONTACT_US = "jfkt8form/contactUs";
const FORM_STAY_UPTO_DATE = "jfkt8form/stayUpToDate";
const FORM_MY_T8 = "jfkt8form/myt8";
const SMS_BUSINESS_OPPORTUNITIES = "sms/businessOpportunities";
const SMS_EMPLOYMENT_OPPORTUNITIES = "sms/employmentOpportunities";
const SMS_STAY_UPTO_DATE = "sms/stayUpToDate";
const SMS_MY_T8 = "sms/myt8";
const SITE_THE_NEW_JFKT8 = "site/thenewjfkt8";

const addMember = async (formData, formType) => {
  const mobile = formData.mobile || formData.mobileNumber;
  const company = formData.company || formData.companyName;
  const homeAddress1 = formData.homeAddress1 || "";
  const homeAddress2 = formData.homeAddress2 || "";
  const businessAddress1 = formData.businessAddress1 || "";
  const businessAddress2 = formData.businessAddress2 || "";

  // const address1 = formData.businessAddress1 || formData.homeAddress1 || "";
  // const address2 = formData.businessAddress2 || formData.homeAddress2 || "";

  const airportExperience = formData.airportEmploymentExperience || formData.airportExperience || "";
  const tagsArr = [];
  let tags = Object.entries(formData)
    .filter((item) => (item[0].includes("bi/") || item[0].includes("cert/") || item[0].includes("ei/") || item[0].includes("stayuptodate/")) && item[1])
    .map((item) => item[0]);
  tags = [...BUSINESS_INTEREST_OPTIONS, ...CERTIFICATIONS_OPTIONS, ...EMPLOYMENT_INTEREST_OPTIONS, ...STAY_UP_TO_DATE_INTEREST_OPTIONS].forEach((item) =>
    tags.includes(item.key) ? tagsArr.push(item.value) : null
  );

  // ADD THE TAG FOR SITE
  tagsArr.push(SITE_THE_NEW_JFKT8);

  // TAG TO ADD ONLY IF ADVANCED NETWORK OPTION IS CHECKED ON THE FORM
  if (formData.advancedNetworkNews) {
    tagsArr.push(JFK_SUPPLIER_DIVERSITY);
  }

  switch (formType) {
    case FORM_TYPES.CONTACT_US:
      tagsArr.push(FORM_CONTACT_US);
      break;
    case FORM_TYPES.STAY_UP_TO_DATE:
    case FORM_TYPES.EMAIL_ONLY:
      // if (formType == FORM_TYPES.STAY_UP_TO_DATE && formData.textUpdates) {
      //   tagsArr.push(SMS_STAY_UPTO_DATE);
      // }
      tagsArr.push(FORM_STAY_UPTO_DATE);
      break;
    case FORM_TYPES.BUSINESS_OPPORTUNITY:
      tagsArr.push(FORM_BUSINESS_OPPORTUNITY);
      // if (formData.textUpdates) {
      //   tagsArr.push(SMS_BUSINESS_OPPORTUNITIES);
      // }
      break;
    case FORM_TYPES.EMPLOYMENT_OPPORTUNITY:
      // if (formData.textUpdates) {
      //   tagsArr.push(SMS_EMPLOYMENT_OPPORTUNITIES);
      // }
      tagsArr.push(FORM_EMPLOYMENT_OPPORTUNITY);
      break;
    case FORM_TYPES.MY_T8:
      tagsArr.push(FORM_MY_T8);
      if (formData.textUpdates) {
        tagsArr.push(SMS_MY_T8);
      }
      break;
  }
  // const mergeFields = {
  //   FNAME: formData.firstName,
  //   LNAME: formData.lastName,
  //   MMERGE14: mobile,
  //   ORG: company,
  //   MESSAGE: formData.message,
  //   MMERGE15: formData.website,
  //   BUS_ABT: formData.aboutBusiness,
  //   AIRPORTEXP: airportExperience,
  //   BUS_YEARS: formData.yearsInBusiness,
  // };

  const mergeFields = {
    FNAME: formData.firstName,
    LNAME: formData.lastName,
    PHONE: mobile,
    COMPANY: company,
    MESSAGE: formData.message,
    WEBSITE: formData.website,
    BUS_ABT: formData.aboutBusiness,
    AIRPORTEXP: airportExperience,
    BUS_YEARS: formData.yearsInBusiness,
  };

  // if (address1 || address2) {
  //   mergeFields["ADDRESS"] = {
  //     addr1: address1,
  //     addr2: address2,
  //     city: formData.city,
  //     state: formData.state,
  //     zip: `${formData.zipCode}`,
  //   };
  // }

  if (homeAddress1 || homeAddress2) {
    mergeFields["ADDRESS"] = {
      addr1: homeAddress1,
      addr2: homeAddress2,
      city: formData.city,
      state: formData.state,
      zip: `${formData.zipCode}`,
    };
  }

  if (businessAddress1 || businessAddress2) {
    mergeFields["B_ADDRESS"] = {
      addr1: businessAddress1,
      addr2: businessAddress2,
      city: formData.city,
      state: formData.state,
      zip: `${formData.zipCode}`,
    };
  }

  // const mailChimpData = {
  //   email_address: formData.email,
  //   status: "subscribed",
  //   merge_fields: mergeFields,
  // };

  const subscriber = {
    email_address: formData.email,
    status_if_new: "subscribed",
    merge_fields: mergeFields,
  };

  const subscriberHash = crypto.createHash("md5").update(subscriber.email_address.toLowerCase()).digest("hex");

  // if (tagsArr.length > 0) {
  //   mailChimpData["tags"] = tagsArr;
  // }

  if (tagsArr.length > 0) {
    subscriber["tags"] = tagsArr;
  }

  try {
    // await mailchimp.lists.addListMember(
    //   process.env.MAILCHIMP_AUDIENCE_ID,
    //   mailChimpData
    // );

    await mailchimp.lists.setListMember(process.env.MAILCHIMP_AUDIENCE_ID, subscriberHash, subscriber);
  } catch (error) {
    console.log("mailchimp error");
    console.log(JSON.stringify(error));
  }
};

module.exports = { addMember };
