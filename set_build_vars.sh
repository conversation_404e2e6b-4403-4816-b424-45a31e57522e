#!bin/bash
set -e

PARAMETER_PREFIX=$1
input_file="vars_parameters"

## get value form aws ssm parameter store
parameter () {
  aws --profile $_AWS_PROFILE --region $_AWS_REGION ssm get-parameter --name ${PARAMETER_PREFIX}$1 --with-decryption --query "Parameter.Value" --output text
}

## remove existing .env
rm -rf .env

## read the input file and create .env file
while IFS= read -r line
do
  KEY=$(echo $line | cut -d '=' -f 1)
  PARAMETER=$(echo $line | cut -d '=' -f 2)
  VALUE=$(parameter $PARAMETER)
  echo $KEY=$VALUE >> .env
done < "$input_file"
