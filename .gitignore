# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
/node_modules
/.pnp
.pnp.js

# testing
/coverage

# next.js
/.next/
/out/

# production
/build

# misc
.DS_Store
*.pem

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*

# local env files
.env
.env*.local

# vercel
.vercel

# typescript
*.tsbuildinfo
next-env.d.ts

# terraform
terraform.tfstate*
.terraform*
tfplan
# Local .terragrunt directories
**/.terragrunt-cache
**/.terragrunt-source-manifest
!.terraform-version
!.terragrunt-version
!tf-init/envs/*/terraform.tfstate
!**/.terraform.lock.hcl

.DS_Store
**/.DS_Store

.zip
**/**.zip

lambda/events
lambda/node_modules

## next
next.out
public/assets/sitemap.xml
